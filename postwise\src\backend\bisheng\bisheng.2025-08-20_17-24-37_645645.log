[2025-08-20 17:24:37.622461] [INFO process-70468-67360 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-20 17:24:42.108083] [INFO process-70468-67360 bisheng.interface.utils:92] - trace=1 LLM caching setup with InMemoryCache
[2025-08-20 17:25:49.742711] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7edefb928e3146c6b9c1912efbf92ce1 GET /api/v1/web/config
[2025-08-20 17:25:49.792841] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7edefb928e3146c6b9c1912efbf92ce1 GET /api/v1/web/config 200 timecost=51.13
[2025-08-20 17:26:19.626152] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2ced229a98ee44a7b125b129790ca2e9 GET /api/v1/user/info
[2025-08-20 17:26:19.635657] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=85ca89d1f07b443b9590c6f681a8bab0 GET /api/v1/all
[2025-08-20 17:26:19.655509] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=826a1978e7234648833e738096b8d1c2 GET /api/v1/env
[2025-08-20 17:26:19.663468] [INFO process-70468-69748 bisheng.interface.custom.utils:343] - trace=85ca89d1f07b443b9590c6f681a8bab0 Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-20 17:26:19.680127] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2ced229a98ee44a7b125b129790ca2e9 GET /api/v1/user/info 401 timecost=53.974
[2025-08-20 17:26:19.867260] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=884ae7df535c470388d699bdcdbdac56 GET /api/v1/user/get_captcha
[2025-08-20 17:26:20.792567] [INFO process-70468-67360 bisheng.api.v1.user:673] - trace=884ae7df535c470388d699bdcdbdac56 get_captcha captcha_char=XW3I
[2025-08-20 17:26:21.027280] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=884ae7df535c470388d699bdcdbdac56 GET /api/v1/user/get_captcha 200 timecost=1160.019
[2025-08-20 17:26:21.048237] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=826a1978e7234648833e738096b8d1c2 GET /api/v1/env 200 timecost=1392.727
[2025-08-20 17:26:21.049594] [INFO process-70468-69748 bisheng.interface.custom.utils:354] - trace=85ca89d1f07b443b9590c6f681a8bab0 Loading 1 component(s) from category custom_components
[2025-08-20 17:26:21.078987] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4bf8ab08a58a4b75b7caa65336497189 GET /api/v1/workstation/config
[2025-08-20 17:26:22.162174] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=85ca89d1f07b443b9590c6f681a8bab0 GET /api/v1/all 200 timecost=2526.517
[2025-08-20 17:26:22.173271] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4bf8ab08a58a4b75b7caa65336497189 GET /api/v1/workstation/config 401 timecost=1094.284
[2025-08-20 17:26:25.807435] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=41ccee85d0f44aacb339896886fb7d05 GET /api/v1/user/public_key
[2025-08-20 17:26:25.889542] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=41ccee85d0f44aacb339896886fb7d05 GET /api/v1/user/public_key 200 timecost=82.107
[2025-08-20 17:26:26.070507] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=cc53a397fd924785b9e65f9aa400b24a POST /api/v1/user/login
[2025-08-20 17:26:26.206286] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=cc53a397fd924785b9e65f9aa400b24a POST /api/v1/user/login 200 timecost=135.779
[2025-08-20 17:26:26.219807] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6289d073310a4366aced1c9b5dfabc8f GET /api/v1/user/get_captcha
[2025-08-20 17:26:26.234044] [INFO process-70468-67360 bisheng.api.v1.user:673] - trace=6289d073310a4366aced1c9b5dfabc8f get_captcha captcha_char=0VsC
[2025-08-20 17:26:26.326097] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6289d073310a4366aced1c9b5dfabc8f GET /api/v1/user/get_captcha 200 timecost=106.29
[2025-08-20 17:26:29.506015] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f6ffbe5b14554fecbd13093061215dd9 GET /api/v1/user/public_key
[2025-08-20 17:26:29.516237] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f6ffbe5b14554fecbd13093061215dd9 GET /api/v1/user/public_key 200 timecost=10.222
[2025-08-20 17:26:29.837918] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3f08bb6f4ade469183b46a2b780ff412 POST /api/v1/user/login
[2025-08-20 17:26:29.841923] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=42ffb00c51a04eb0aab2393018ac1242 GET /api/v1/user/get_captcha
[2025-08-20 17:26:29.858105] [INFO process-70468-67360 bisheng.api.v1.user:673] - trace=42ffb00c51a04eb0aab2393018ac1242 get_captcha captcha_char=xMqG
[2025-08-20 17:26:29.958236] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=42ffb00c51a04eb0aab2393018ac1242 GET /api/v1/user/get_captcha 200 timecost=115.307
[2025-08-20 17:26:30.282483] [INFO process-70468-67360 bisheng.api.services.audit_log:399] - trace=3f08bb6f4ade469183b46a2b780ff412 act=user_login user=Mao ip=127.0.0.1 user_id=12
[2025-08-20 17:26:30.492241] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3f08bb6f4ade469183b46a2b780ff412 POST /api/v1/user/login 200 timecost=654.323
[2025-08-20 17:26:30.647370] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=39b358367ab249e181b930d01d595d43 GET /api/v1/web/config
[2025-08-20 17:26:30.668140] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=39b358367ab249e181b930d01d595d43 GET /api/v1/web/config 200 timecost=20.77
[2025-08-20 17:26:33.106371] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2e8ee316f6af45bebf59bd70e05c9431 GET /api/v1/user/info
[2025-08-20 17:26:33.117188] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ebf506d3ad1a461ebaa07dd29ae5ea02 GET /api/v1/env
[2025-08-20 17:26:33.131065] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2c6f538a490741d2946bb40de45e1be3 GET /api/v1/all
[2025-08-20 17:26:33.167583] [INFO process-70468-69728 bisheng.interface.custom.utils:343] - trace=2c6f538a490741d2946bb40de45e1be3 Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-20 17:26:34.120961] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2e8ee316f6af45bebf59bd70e05c9431 GET /api/v1/user/info 200 timecost=1014.591
[2025-08-20 17:26:34.145052] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ebf506d3ad1a461ebaa07dd29ae5ea02 GET /api/v1/env 200 timecost=1027.864
[2025-08-20 17:26:34.231237] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d7704eca15214d65914aa4d5331747ff GET /api/v1/component
[2025-08-20 17:26:34.472138] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7e0e1da274554a47a402db1232cc2ceb GET /api/v1/tag/home
[2025-08-20 17:26:34.504406] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0e9c7e1bd97a40d085c362b78ae90dee GET /api/v1/tag
[2025-08-20 17:26:34.530822] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d7704eca15214d65914aa4d5331747ff GET /api/v1/component 200 timecost=299.585
[2025-08-20 17:26:34.857724] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=08ec88cbb15d40e6bc641db09f4e2427 GET /api/v1/workstation/config
[2025-08-20 17:26:34.885423] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3c628fb8e3e2477196c96d2d4107ca96 GET /api/v1/chat/list
[2025-08-20 17:26:34.914859] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7e0e1da274554a47a402db1232cc2ceb GET /api/v1/tag/home 200 timecost=443.722
[2025-08-20 17:26:34.915924] [INFO process-70468-69728 bisheng.interface.custom.utils:354] - trace=2c6f538a490741d2946bb40de45e1be3 Loading 1 component(s) from category custom_components
[2025-08-20 17:26:34.934721] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=44a37e567fcc4062b69e63bf169743b0 GET /api/v1/chat/online
[2025-08-20 17:26:35.225028] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0e9c7e1bd97a40d085c362b78ae90dee GET /api/v1/tag 200 timecost=720.622
[2025-08-20 17:26:35.789915] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2c6f538a490741d2946bb40de45e1be3 GET /api/v1/all 200 timecost=2658.849
[2025-08-20 17:26:36.240236] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=031f26b175c14172b3f7313a60cffa51 GET /api/v1/tag
[2025-08-20 17:26:36.592996] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=44a37e567fcc4062b69e63bf169743b0 GET /api/v1/chat/online 200 timecost=1658.94
[2025-08-20 17:26:36.698403] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3c628fb8e3e2477196c96d2d4107ca96 GET /api/v1/chat/list 200 timecost=1812.979
[2025-08-20 17:26:36.709989] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=08ec88cbb15d40e6bc641db09f4e2427 GET /api/v1/workstation/config 200 timecost=1852.264
[2025-08-20 17:26:36.731150] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=031f26b175c14172b3f7313a60cffa51 GET /api/v1/tag 200 timecost=492.914
[2025-08-20 17:26:38.730174] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=21d00a7f5d7944039ea53ebe0660fae6 GET /api/v1/skill/template
[2025-08-20 17:26:38.736774] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=203b8fd9d3554fbaba074636003d5ab6 GET /api/v1/tag
[2025-08-20 17:26:38.920373] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=21d00a7f5d7944039ea53ebe0660fae6 GET /api/v1/skill/template 200 timecost=190.199
[2025-08-20 17:26:38.930927] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=203b8fd9d3554fbaba074636003d5ab6 GET /api/v1/tag 200 timecost=194.153
[2025-08-20 17:26:39.634746] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a6aba39b4c724e868cd575e8ed2bf552 GET /api/v1/workflow/list
[2025-08-20 17:26:39.776980] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a6aba39b4c724e868cd575e8ed2bf552 GET /api/v1/workflow/list 200 timecost=142.234
[2025-08-20 17:26:39.900030] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f770c89e31754df9a93ed945f60cdedd GET /api/v1/skill/template
[2025-08-20 17:26:40.018422] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f770c89e31754df9a93ed945f60cdedd GET /api/v1/skill/template 200 timecost=118.392
[2025-08-20 17:27:03.634938] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0b871cfdbd09467faec00e57bd8d8e3f GET /api/v1/web/config
[2025-08-20 17:27:03.662444] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0b871cfdbd09467faec00e57bd8d8e3f GET /api/v1/web/config 200 timecost=28.505
[2025-08-20 17:27:16.427086] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=80d0fd48e56e485dbbadf37d25750839 GET /api/v1/user/info
[2025-08-20 17:27:16.434966] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4451b6c6098f499ea2a647c54693ecfe GET /api/v1/env
[2025-08-20 17:27:16.444599] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2e347b9f7d8b4ac9a0d6b4b3b3250f91 GET /api/v1/all
[2025-08-20 17:27:16.477001] [INFO process-70468-18972 bisheng.interface.custom.utils:343] - trace=2e347b9f7d8b4ac9a0d6b4b3b3250f91 Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-20 17:27:17.115847] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=80d0fd48e56e485dbbadf37d25750839 GET /api/v1/user/info 200 timecost=688.761
[2025-08-20 17:27:17.131463] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4451b6c6098f499ea2a647c54693ecfe GET /api/v1/env 200 timecost=696.497
[2025-08-20 17:27:17.154143] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=189670748fbc49c28be03e18444de35b GET /api/v1/component
[2025-08-20 17:27:17.316172] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e5dac425de384fc992c37612f17e23bb GET /api/v1/skill/template
[2025-08-20 17:27:17.339226] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=75f3ac912720407dad082672e4b3b1ae GET /api/v1/tag
[2025-08-20 17:27:17.356205] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=189670748fbc49c28be03e18444de35b GET /api/v1/component 200 timecost=203.062
[2025-08-20 17:27:17.673672] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=823432fffe6f41569d75919a93282375 GET /api/v1/workstation/config
[2025-08-20 17:27:17.700271] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e5dac425de384fc992c37612f17e23bb GET /api/v1/skill/template 200 timecost=384.099
[2025-08-20 17:27:17.861750] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=75f3ac912720407dad082672e4b3b1ae GET /api/v1/tag 200 timecost=522.524
[2025-08-20 17:27:17.879120] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d7ff7fb76898443f900d69ee95961380 GET /api/v1/workflow/list
[2025-08-20 17:27:17.881646] [INFO process-70468-18972 bisheng.interface.custom.utils:354] - trace=2e347b9f7d8b4ac9a0d6b4b3b3250f91 Loading 1 component(s) from category custom_components
[2025-08-20 17:27:18.918523] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2e347b9f7d8b4ac9a0d6b4b3b3250f91 GET /api/v1/all 200 timecost=2474.938
[2025-08-20 17:27:18.933770] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=823432fffe6f41569d75919a93282375 GET /api/v1/workstation/config 200 timecost=1260.097
[2025-08-20 17:27:18.959292] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d7ff7fb76898443f900d69ee95961380 GET /api/v1/workflow/list 200 timecost=1080.171
[2025-08-20 17:27:19.313186] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=455774f999ee4c2aa63fb75e836b523a GET /api/v1/skill/template
[2025-08-20 17:27:19.425151] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=455774f999ee4c2aa63fb75e836b523a GET /api/v1/skill/template 200 timecost=111.966
[2025-08-20 17:27:28.440904] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4737306a314a4560954e9f0d999d15b9 GET /api/v1/skill/template
[2025-08-20 17:27:28.550859] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4737306a314a4560954e9f0d999d15b9 GET /api/v1/skill/template 200 timecost=109.955
[2025-08-20 17:27:30.319036] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=724d4bd28e8f467fa4e9344279d26c91 GET /api/v1/llm/workflow
[2025-08-20 17:27:30.403692] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9c8371607e8a4752a0ab0e1148a6316d GET /api/v1/llm/assistant
[2025-08-20 17:27:30.493323] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=724d4bd28e8f467fa4e9344279d26c91 GET /api/v1/llm/workflow 200 timecost=174.287
[2025-08-20 17:27:30.507005] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9c8371607e8a4752a0ab0e1148a6316d GET /api/v1/llm/assistant 200 timecost=103.313
[2025-08-20 17:27:30.586880] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a0d175c025324eda964381f6fb122628 POST /api/v1/workflow/create
[2025-08-20 17:27:30.824348] [INFO process-70468-67596 bisheng.api.services.flow:418] - trace=a0d175c025324eda964381f6fb122628 create_flow_hook flow: 636c01d4f3fa498ba5ea39a47f6cb93b, user_payload: 12
[2025-08-20 17:27:30.949731] [INFO process-70468-67596 bisheng.api.services.audit_log:177] - trace=a0d175c025324eda964381f6fb122628 act=create_build_flow user=Mao ip=127.0.0.1 flow=636c01d4f3fa498ba5ea39a47f6cb93b
[2025-08-20 17:27:31.096024] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a0d175c025324eda964381f6fb122628 POST /api/v1/workflow/create 201 timecost=509.145
[2025-08-20 17:27:31.122675] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ba8228a45f5c417f9aff976bd353e190 GET /api/v1/flows/636c01d4f3fa498ba5ea39a47f6cb93b
[2025-08-20 17:27:31.273472] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ba8228a45f5c417f9aff976bd353e190 GET /api/v1/flows/636c01d4f3fa498ba5ea39a47f6cb93b 200 timecost=150.797
[2025-08-20 17:27:32.052020] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=fbd60110a07c40e19db3ad8001ed353b GET /api/v1/workflow/versions
[2025-08-20 17:27:32.062281] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a70881668bed4f81a1aa6fa4158980f7 GET /api/v1/assistant/tool_list
[2025-08-20 17:27:32.072347] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9c0f8eaccf104368b0bda40c23b41828 GET /api/v1/knowledge
[2025-08-20 17:27:32.083383] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=aa66ad80514d4d4193ce09c6fdf4ac5c GET /api/v1/knowledge
[2025-08-20 17:27:32.105014] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=53b3b6e1d9e243a48489b08e50e9365d GET /api/v1/llm
[2025-08-20 17:27:32.114828] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b1baad5886a0432f8470a855593a4615 GET /api/v1/llm/workflow
[2025-08-20 17:27:32.943846] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=fbd60110a07c40e19db3ad8001ed353b GET /api/v1/workflow/versions 200 timecost=891.826
[2025-08-20 17:27:32.977342] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b1baad5886a0432f8470a855593a4615 GET /api/v1/llm/workflow 200 timecost=862.514
[2025-08-20 17:27:33.088259] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=53b3b6e1d9e243a48489b08e50e9365d GET /api/v1/llm 200 timecost=983.244
[2025-08-20 17:27:33.094810] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a70881668bed4f81a1aa6fa4158980f7 GET /api/v1/assistant/tool_list 200 timecost=1032.529
[2025-08-20 17:27:33.134981] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9c0f8eaccf104368b0bda40c23b41828 GET /api/v1/knowledge 200 timecost=1061.62
[2025-08-20 17:27:33.182316] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=aa66ad80514d4d4193ce09c6fdf4ac5c GET /api/v1/knowledge 200 timecost=1099.93
[2025-08-20 17:28:01.209770] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=81651131452b4266a56f3897ef2c4d87 GET /api/v1/skill/template
[2025-08-20 17:28:01.369555] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=81651131452b4266a56f3897ef2c4d87 GET /api/v1/skill/template 200 timecost=160.379
[2025-08-20 17:28:01.803259] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d33ef1da247143418fea366e9024a965 GET /api/v1/workflow/list
[2025-08-20 17:28:01.979118] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d33ef1da247143418fea366e9024a965 GET /api/v1/workflow/list 200 timecost=175.859
[2025-08-20 17:28:02.015388] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e9988f3045c64115a29e59b48ac63c03 GET /api/v1/skill/template
[2025-08-20 17:28:02.150831] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e9988f3045c64115a29e59b48ac63c03 GET /api/v1/skill/template 200 timecost=135.443
[2025-08-20 17:28:08.734320] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6249e8977602429eaa7a21e088507715 POST /api/v1/workflow/create
[2025-08-20 17:28:08.840576] [INFO process-70468-68788 bisheng.database.base:25] - trace=6249e8977602429eaa7a21e088507715 Session rollback because of exception:10529: 工作流名称重复
[2025-08-20 17:28:08.852895] [ERROR process-70468-68788 __main__:33] - trace=6249e8977602429eaa7a21e088507715 POST http://127.0.0.1:7860/api/v1/workflow/create 10529: 工作流名称重复
[2025-08-20 17:28:08.859915] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6249e8977602429eaa7a21e088507715 POST /api/v1/workflow/create 200 timecost=125.595
[2025-08-20 17:28:11.278798] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=35786c684a06448faabb6a39f8195bde POST /api/v1/workflow/create
[2025-08-20 17:28:11.512952] [INFO process-70468-34648 bisheng.api.services.flow:418] - trace=35786c684a06448faabb6a39f8195bde create_flow_hook flow: 0e1478d85ad74fa5b47b9de0d2d2f51f, user_payload: 12
[2025-08-20 17:28:11.586994] [INFO process-70468-34648 bisheng.api.services.audit_log:177] - trace=35786c684a06448faabb6a39f8195bde act=create_build_flow user=Mao ip=127.0.0.1 flow=0e1478d85ad74fa5b47b9de0d2d2f51f
[2025-08-20 17:28:11.770792] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=35786c684a06448faabb6a39f8195bde POST /api/v1/workflow/create 201 timecost=491.995
[2025-08-20 17:28:11.799305] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f324a04c95694c5592589f0ac86fe0a9 GET /api/v1/flows/0e1478d85ad74fa5b47b9de0d2d2f51f
[2025-08-20 17:28:11.897977] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f324a04c95694c5592589f0ac86fe0a9 GET /api/v1/flows/0e1478d85ad74fa5b47b9de0d2d2f51f 200 timecost=99.671
[2025-08-20 17:28:12.502301] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e3f0037e101b40a8b88f9af225582129 GET /api/v1/workflow/versions
[2025-08-20 17:28:12.515369] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=23c2d1e1712d49599cf6ce97198a5b42 GET /api/v1/assistant/tool_list
[2025-08-20 17:28:12.628814] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e3f0037e101b40a8b88f9af225582129 GET /api/v1/workflow/versions 200 timecost=126.513
[2025-08-20 17:28:12.685373] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=23c2d1e1712d49599cf6ce97198a5b42 GET /api/v1/assistant/tool_list 200 timecost=170.004
[2025-08-20 17:28:15.719603] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=266ee90e6c1540ba9db849bfade33e67 GET /api/v1/llm
[2025-08-20 17:28:15.726602] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c44a68c3b8614ab0a0d4b8f2ba409092 GET /api/v1/llm/workflow
[2025-08-20 17:28:16.053959] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c44a68c3b8614ab0a0d4b8f2ba409092 GET /api/v1/llm/workflow 200 timecost=327.357
[2025-08-20 17:28:16.095882] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=266ee90e6c1540ba9db849bfade33e67 GET /api/v1/llm 200 timecost=376.279
[2025-08-20 17:29:54.000324] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=65a57b330ded4181abfdf62208733e9c POST /api/v1/workflow/run_once
[2025-08-20 17:29:54.454096] [INFO process-70468-67360 bisheng.workflow.callback.llm_callback:35] - trace=65a57b330ded4181abfdf62208733e9c on_llm_new_token True outkey=output
[2025-08-20 17:29:59.586467] [INFO process-70468-67360 httpx._client:1025] - trace=65a57b330ded4181abfdf62208733e9c HTTP Request: POST http://117.145.66.250:1/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
[2025-08-20 17:30:00.025002] [INFO process-70468-67360 openai._base_client:1071] - trace=65a57b330ded4181abfdf62208733e9c Retrying request to /chat/completions in 0.407134 seconds
[2025-08-20 17:30:05.493512] [INFO process-70468-67360 httpx._client:1025] - trace=65a57b330ded4181abfdf62208733e9c HTTP Request: POST http://117.145.66.250:1/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
[2025-08-20 17:30:06.070028] [INFO process-70468-67360 openai._base_client:1071] - trace=65a57b330ded4181abfdf62208733e9c Retrying request to /chat/completions in 0.789834 seconds
[2025-08-20 17:30:11.938259] [INFO process-70468-67360 httpx._client:1025] - trace=65a57b330ded4181abfdf62208733e9c HTTP Request: POST http://117.145.66.250:1/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
[2025-08-20 17:30:12.406291] [ERROR process-70468-59452 __main__:33] - trace=1 POST http://127.0.0.1:7860/api/v1/workflow/run_once Error code: 502
[2025-08-20 17:30:27.206457] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d8f9f2422abb48c699d8a3d6c017bab9 GET /api/v1/skill/template
[2025-08-20 17:30:27.346024] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d8f9f2422abb48c699d8a3d6c017bab9 GET /api/v1/skill/template 200 timecost=139.567
[2025-08-20 17:30:27.798074] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c44c572960714fa38e889b47009fd6be GET /api/v1/workflow/list
[2025-08-20 17:30:27.954579] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c44c572960714fa38e889b47009fd6be GET /api/v1/workflow/list 200 timecost=156.504
[2025-08-20 17:30:27.991695] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a60c35f80a744d51b1e99e731e997f51 GET /api/v1/skill/template
[2025-08-20 17:30:28.097330] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a60c35f80a744d51b1e99e731e997f51 GET /api/v1/skill/template 200 timecost=105.634
[2025-08-20 17:30:32.530178] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b5eae2b957fc45dfa5e0432e1f6d0154 GET /api/v1/assistant/tool_list
[2025-08-20 17:30:32.610738] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=04926f50303b4a72a82e483a82050cb8 GET /api/v1/assistant/info/14d960cb72f14e30a0a3df071636478f
[2025-08-20 17:30:32.773480] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b5eae2b957fc45dfa5e0432e1f6d0154 GET /api/v1/assistant/tool_list 200 timecost=244.261
[2025-08-20 17:30:32.790927] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=04926f50303b4a72a82e483a82050cb8 GET /api/v1/assistant/info/14d960cb72f14e30a0a3df071636478f 200 timecost=180.189
[2025-08-20 17:30:32.830861] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3b4bf6e167c94357bdd71ffee1f3080c GET /api/v1/flows/
[2025-08-20 17:30:33.008344] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3b4bf6e167c94357bdd71ffee1f3080c GET /api/v1/flows/ 200 timecost=177.483
[2025-08-20 17:30:36.450202] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=19476eb63bcb410d996b35f34038164f GET /api/v1/skill/template
[2025-08-20 17:30:36.576605] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=19476eb63bcb410d996b35f34038164f GET /api/v1/skill/template 200 timecost=126.402
[2025-08-20 17:30:37.355410] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=828c39040d8944058c153398158b5cfd GET /api/v1/workflow/list
[2025-08-20 17:30:37.497431] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=828c39040d8944058c153398158b5cfd GET /api/v1/workflow/list 200 timecost=142.021
[2025-08-20 17:30:37.621752] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b3ec2b21f736429d9f6b4865fff08e53 GET /api/v1/skill/template
[2025-08-20 17:30:37.725692] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b3ec2b21f736429d9f6b4865fff08e53 GET /api/v1/skill/template 200 timecost=103.94
[2025-08-20 17:30:38.785126] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3307085195fd403087378ff692faf2eb GET /api/v1/workflow/list
[2025-08-20 17:30:38.942236] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3307085195fd403087378ff692faf2eb GET /api/v1/workflow/list 200 timecost=157.109
[2025-08-20 17:30:39.287695] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7caff01b49554ae7859de74df34bdc2e GET /api/v1/skill/template
[2025-08-20 17:30:39.409902] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7caff01b49554ae7859de74df34bdc2e GET /api/v1/skill/template 200 timecost=122.207
[2025-08-20 17:30:42.037778] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4717c68831c14e8aaab6ad0056486b32 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627
[2025-08-20 17:30:42.137055] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4717c68831c14e8aaab6ad0056486b32 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627 200 timecost=99.277
[2025-08-20 17:30:42.710690] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=37aa213410ef4800b0719ea37e96315b GET /api/v1/workflow/versions
[2025-08-20 17:30:42.718703] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=044bfabd50bf4a188b773283bf9cb1af GET /api/v1/assistant/tool_list
[2025-08-20 17:30:42.723703] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=01dc93de65504ffe8127c2d51709fd50 GET /api/v1/knowledge
[2025-08-20 17:30:42.734957] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=72cfd6eeefbb403ebfb850f355fe5a6c GET /api/v1/llm
[2025-08-20 17:30:42.738982] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=13febb4ceb1d4cefbe788d776989f7ca GET /api/v1/llm/workflow
[2025-08-20 17:30:43.360378] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=37aa213410ef4800b0719ea37e96315b GET /api/v1/workflow/versions 200 timecost=649.688
[2025-08-20 17:30:43.410828] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=13febb4ceb1d4cefbe788d776989f7ca GET /api/v1/llm/workflow 200 timecost=671.846
[2025-08-20 17:30:43.514561] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=72cfd6eeefbb403ebfb850f355fe5a6c GET /api/v1/llm 200 timecost=779.605
[2025-08-20 17:30:43.527273] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=044bfabd50bf4a188b773283bf9cb1af GET /api/v1/assistant/tool_list 200 timecost=808.57
[2025-08-20 17:30:43.575773] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=01dc93de65504ffe8127c2d51709fd50 GET /api/v1/knowledge 200 timecost=851.084
[2025-08-20 17:30:46.128472] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ad906654cbc045fca2a15e6b3abe0125 GET /api/v1/knowledge/info
[2025-08-20 17:30:46.236289] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ad906654cbc045fca2a15e6b3abe0125 GET /api/v1/knowledge/info 200 timecost=107.818
[2025-08-20 17:30:46.590564] [INFO process-70468-72480 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 17:30:46.599083] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=4b68f56b65954723b071d5de0f9431d6 trace_id=ff3cd594fa8e46df9e899c6097498080 message={'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id': 12, 'flow_type': 10, 'update_time': '2025-08-07T15:12:45', 'id': '0c26e44b161448ca913617e3c857d627', 'status': 2, 'description': '', 'guide_word': None, 'create_time': '2025-07-31T15:46:27', 'nodes': [{'id': 'start_25626', 'data': {'v': '1', 'id': 'start_25626', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': [''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': '1afc3c', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 614}, 'position': {'x': 720, 'y': 20}, 'selected': False}, {'id': 'input_1fb3a', 'data': {'v': '2', 'id': 'input_1fb3a', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '输入文本内容', 'global': 'key'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 421}, 'position': {'x': 1155, 'y': 15}, 'selected': False}, {'id': 'rag_63d89', 'data': {'v': '1', 'id': 'rag_63d89', 'name': '文档知识库问答', 'type': 'rag', 'description': '根据用户问题从知识库中检索相关内容，结合检索结果调用大模型生成最终结果，支持多个问题并行执行。', 'group_params': [{'name': '知识库检索设置', 'params': [{'key': 'user_question', 'help': '当选择多个问题时，将会多次运行本节点，每次运行时从批量问题中取一项进行处理。', 'test': 'var', 'type': 'user_question', 'label': '用户问题', 'value': ['input_1fb3a.user_input'], 'varZh': {'input_1fb3a.user_input': '输入/user_input'}, 'global': 'self=user_prompt', 'linkage': 'output_user_input', 'required': True, 'placeholder': '请选择用户问题'}, {'key': 'knowledge', 'type': 'knowledge_select_multi', 'label': '检索范围', 'value': {'type': 'knowledge', 'value': [{'key': 124, 'label': '比亚迪-秦PLUS-车型知识库'}]}, 'required': True, 'placeholder': '请选择知识库'}, {'key': 'user_auth', 'help': '开启后，只会对用户有使用权限的知识库进行检索。', 'type': 'switch', 'label': '用户知识库权限校验', 'value': False}, {'key': 'max_chunk_size', 'help': '通过此参数控制最终传给模型的知识库检索结果文本长度，超过模型支持的最大上下文长度可能会导致报错。', 'type': 'number', 'label': '检索结果长度', 'value': 15000}, {'key': 'retrieved_result', 'type': 'var', 'label': '检索结果', 'global': 'self=user_prompt'}]}, {'name': 'AI回复生成设置', 'params': [{'key': 'system_prompt', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'required': True}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '用户问题：```{{#rag_63d89.user_question#}}```\n参考文本：```{{#rag_63d89.retrieved_result#}}```\n你的回答：', 'varZh': {'rag_63d89.user_question': 'user_question', 'rag_63d89.retrieved_result': 'retrieved_result'}, 'required': True}, {'key': 'model_id', 'type': 'bisheng_model', 'label': '模型', 'value': 82, 'required': True, 'placeholder': '请在模型管理中配置 LLM 模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output_user_input', 'help': '模型输出内容将会存储在该变量中。', 'type': 'var', 'label': '输出变量', 'value': [{'key': 'output_user_input', 'label': 'output_user_input'}], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1097}, 'position': {'x': 1575, 'y': 15}, 'selected': True}], 'edges': [{'id': 'xy-edge__input_1fb3aright_handle-rag_63d89left_handle', 'type': 'customEdge', 'source': 'input_1fb3a', 'target': 'rag_63d89', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__start_25626right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'start_25626', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__rag_63d89right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'rag_63d89', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': -76.53783044667273, 'y': 24.824977210574275, 'zoom': 0.6116681859617138}}}
[2025-08-20 17:30:46.727113] [INFO process-70468-49076 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 17:30:47.641508] [INFO process-70468-72480 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-20 17:30:47.750114] [INFO process-70468-49076 bisheng.utils.threadpool:67] - trace=ff3cd594fa8e46df9e899c6097498080 async_task_added fun=wrapper_task args=ff3cd594fa8e46df9e899c6097498080
[2025-08-20 17:30:53.780207] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=4b68f56b65954723b071d5de0f9431d6 trace_id=dc5cd88b8c3e47e9bdfa5a8815f851be message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': '你好', 'dialog_files_content': []}, 'message': '你好', 'message_id': '5e41579b12eb4f5fb44cc6e5e8cf8e04', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 17:30:53.806790] [INFO process-70468-72480 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 17:30:53.826044] [INFO process-70468-72132 bisheng.chat.clients.workflow_client:182] - trace=dc5cd88b8c3e47e9bdfa5a8815f851be get user input: {'input_1fb3a': {'data': {'user_input': '你好', 'dialog_files_content': []}, 'message': '你好', 'message_id': '5e41579b12eb4f5fb44cc6e5e8cf8e04', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 17:30:54.824797] [INFO process-70468-72480 bisheng.utils.threadpool:67] - trace=dc5cd88b8c3e47e9bdfa5a8815f851be async_task_added fun=wrapper_task args=dc5cd88b8c3e47e9bdfa5a8815f851be
[2025-08-20 17:31:25.957834] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=4b68f56b65954723b071d5de0f9431d6 trace_id=c3709a107ed94ff7bcc1ec463ed30db5 message={'action': 'stop'}
[2025-08-20 17:31:25.987321] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=4b68f56b65954723b071d5de0f9431d6 trace_id=de23ab988bcd44e29c26c7285094d0d1 message={'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id': 12, 'flow_type': 10, 'update_time': '2025-08-07T15:12:45', 'id': '0c26e44b161448ca913617e3c857d627', 'status': 2, 'description': '', 'guide_word': None, 'create_time': '2025-07-31T15:46:27', 'nodes': [{'id': 'start_25626', 'data': {'v': '1', 'id': 'start_25626', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': [''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': '1afc3c', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 614}, 'position': {'x': 720, 'y': 20}, 'selected': False}, {'id': 'input_1fb3a', 'data': {'v': '2', 'id': 'input_1fb3a', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '输入文本内容', 'global': 'key'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 421}, 'position': {'x': 1155, 'y': 15}, 'selected': False}, {'id': 'rag_63d89', 'data': {'v': '1', 'id': 'rag_63d89', 'name': '文档知识库问答', 'type': 'rag', 'description': '根据用户问题从知识库中检索相关内容，结合检索结果调用大模型生成最终结果，支持多个问题并行执行。', 'group_params': [{'name': '知识库检索设置', 'params': [{'key': 'user_question', 'help': '当选择多个问题时，将会多次运行本节点，每次运行时从批量问题中取一项进行处理。', 'test': 'var', 'type': 'user_question', 'label': '用户问题', 'value': ['input_1fb3a.user_input'], 'varZh': {'input_1fb3a.user_input': '输入/user_input'}, 'global': 'self=user_prompt', 'linkage': 'output_user_input', 'required': True, 'placeholder': '请选择用户问题'}, {'key': 'knowledge', 'type': 'knowledge_select_multi', 'label': '检索范围', 'value': {'type': 'knowledge', 'value': [{'key': 124, 'label': '比亚迪-秦PLUS-车型知识库'}]}, 'required': True, 'placeholder': '请选择知识库'}, {'key': 'user_auth', 'help': '开启后，只会对用户有使用权限的知识库进行检索。', 'type': 'switch', 'label': '用户知识库权限校验', 'value': False}, {'key': 'max_chunk_size', 'help': '通过此参数控制最终传给模型的知识库检索结果文本长度，超过模型支持的最大上下文长度可能会导致报错。', 'type': 'number', 'label': '检索结果长度', 'value': 15000}, {'key': 'retrieved_result', 'type': 'var', 'label': '检索结果', 'global': 'self=user_prompt'}]}, {'name': 'AI回复生成设置', 'params': [{'key': 'system_prompt', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'required': True}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '用户问题：```{{#rag_63d89.user_question#}}```\n参考文本：```{{#rag_63d89.retrieved_result#}}```\n你的回答：', 'varZh': {'rag_63d89.user_question': 'user_question', 'rag_63d89.retrieved_result': 'retrieved_result'}, 'required': True}, {'key': 'model_id', 'type': 'bisheng_model', 'label': '模型', 'value': 82, 'required': True, 'placeholder': '请在模型管理中配置 LLM 模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output_user_input', 'help': '模型输出内容将会存储在该变量中。', 'type': 'var', 'label': '输出变量', 'value': [{'key': 'output_user_input', 'label': 'output_user_input'}], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1097}, 'position': {'x': 1575, 'y': 15}, 'selected': True}], 'edges': [{'id': 'xy-edge__input_1fb3aright_handle-rag_63d89left_handle', 'type': 'customEdge', 'source': 'input_1fb3a', 'target': 'rag_63d89', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__start_25626right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'start_25626', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__rag_63d89right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'rag_63d89', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': -76.53783044667273, 'y': 24.824977210574275, 'zoom': 0.6116681859617138}}}
[2025-08-20 17:31:26.153863] [INFO process-70468-71428 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 17:31:26.640140] [ERROR process-70468-67360 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x00000281DC18F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 58718, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x00000281DC18E050>
              └ <__main__.PyDB object at 0x00000281DC161570>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x00000281DC18E0E0>
           └ <__main__.PyDB object at 0x00000281DC161570>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x00000281DBB5EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000002819C52CEE0>
    │       └ <function run at 0x000002819B790E50>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002819B7908B0>
    └ <uvicorn.server.Server object at 0x00000281A34AC670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002819B790940>
           │       │   └ <uvicorn.server.Server object at 0x00000281A34AC670>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000281DC1CD5A0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000281DC1CE050>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000281DC1CE0E0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000281DC08FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=6>
           │    └ <_ProactorSocketTransport fd=8004 read=<_OverlappedFuture finished result=10> write=<_OverlappedFuture finished result=6>>
           └ <_OverlappedFuture finished result=12>

AssertionError: assert f is self._write_fut
[2025-08-20 17:31:27.181658] [INFO process-70468-71428 bisheng.utils.threadpool:67] - trace=de23ab988bcd44e29c26c7285094d0d1 async_task_added fun=wrapper_task args=de23ab988bcd44e29c26c7285094d0d1
[2025-08-20 17:31:32.872459] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=4b68f56b65954723b071d5de0f9431d6 trace_id=692bbb3b3df84f7da049604fb0a375ea message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': '你好', 'dialog_files_content': []}, 'message': '你好', 'message_id': 'f14076ed0f1f4b6daea97467151faec1', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 17:31:32.892144] [INFO process-70468-71932 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 17:31:32.908080] [INFO process-70468-69700 bisheng.chat.clients.workflow_client:182] - trace=692bbb3b3df84f7da049604fb0a375ea get user input: {'input_1fb3a': {'data': {'user_input': '你好', 'dialog_files_content': []}, 'message': '你好', 'message_id': 'f14076ed0f1f4b6daea97467151faec1', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 17:31:33.904445] [INFO process-70468-71932 bisheng.utils.threadpool:67] - trace=692bbb3b3df84f7da049604fb0a375ea async_task_added fun=wrapper_task args=692bbb3b3df84f7da049604fb0a375ea
[2025-08-20 17:34:57.276255] [INFO process-70468-67360 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1005, None)
[2025-08-20 17:34:57.281392] [INFO process-70468-67360 bisheng.chat.manager:181] - trace=1 close_client client_key=4b68f56b65954723b071d5de0f9431d6
[2025-08-20 17:34:57.286664] [ERROR process-70468-67360 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-20 17:34:57.290102] [ERROR process-70468-53164 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 12, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "4b68f56b65954723b071d5de0f9431d6"}', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'chat_id': ''} error: Cannot call "send" once a close message has been sent.
[2025-08-20 17:34:57.294031] [WARNING process-70468-67360 bisheng.chat.manager:179] - trace=1 close_client client_key=4b68f56b65954723b071d5de0f9431d6 not in active_clients
[2025-08-20 17:34:57.605716] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=eab616c2bf614f348ef1d3e83086ac7f GET /api/v1/skill/template
[2025-08-20 17:34:57.703183] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=eab616c2bf614f348ef1d3e83086ac7f GET /api/v1/skill/template 200 timecost=97.466
[2025-08-20 17:34:57.895718] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=37a762fffda6441dbb7c790895c8bbf2 GET /api/v1/workflow/list
[2025-08-20 17:34:58.133484] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=37a762fffda6441dbb7c790895c8bbf2 GET /api/v1/workflow/list 200 timecost=237.766
[2025-08-20 17:34:58.187526] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f28b7f32066248a5863658d8961f2f7a GET /api/v1/skill/template
[2025-08-20 17:34:58.340767] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f28b7f32066248a5863658d8961f2f7a GET /api/v1/skill/template 200 timecost=153.241
[2025-08-20 17:34:58.852649] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=78331ca6e0814bf9a2e4e11f6522ed06 GET /api/v1/llm/knowledge
[2025-08-20 17:34:58.960621] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=78331ca6e0814bf9a2e4e11f6522ed06 GET /api/v1/llm/knowledge 200 timecost=108.704
[2025-08-20 17:34:59.160084] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a0f49e649c1e49e2b4552b535b02f102 GET /api/v1/llm
[2025-08-20 17:34:59.317164] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a0f49e649c1e49e2b4552b535b02f102 GET /api/v1/llm 200 timecost=157.08
[2025-08-20 17:34:59.457877] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=02df2ae2a6a54ad4aa596af935cf70da GET /api/v1/knowledge
[2025-08-20 17:34:59.579876] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=02df2ae2a6a54ad4aa596af935cf70da GET /api/v1/knowledge 200 timecost=122.0
[2025-08-20 17:35:02.194030] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=35e1c213cd474c8e8c0b414af9157f03 GET /api/v1/knowledge/file_list/132
[2025-08-20 17:35:05.049034] [INFO process-70468-68776 elastic_transport._transport:349] - trace=35e1c213cd474c8e8c0b414af9157f03 GET http://172.20.20.220:9200/col_1755240839_4a901af3 [status:200 duration:0.092s]
[2025-08-20 17:35:05.116913] [INFO process-70468-68776 elastic_transport._transport:349] - trace=35e1c213cd474c8e8c0b414af9157f03 POST http://172.20.20.220:9200/col_1755240839_4a901af3/_refresh [status:200 duration:0.052s]
[2025-08-20 17:35:05.273056] [INFO process-70468-68776 elastic_transport._transport:349] - trace=35e1c213cd474c8e8c0b414af9157f03 POST http://172.20.20.220:9200/col_1755240839_4a901af3/_search [status:200 duration:0.144s]
[2025-08-20 17:35:05.301579] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=35e1c213cd474c8e8c0b414af9157f03 GET /api/v1/knowledge/file_list/132 200 timecost=3107.55
[2025-08-20 17:35:05.555848] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bc7fa3127a0745759e44af32150c86af GET /api/v1/knowledge
[2025-08-20 17:35:05.875418] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ca92fb18438b42e1be8f8ab4da344ce9 GET /api/v1/knowledge/file_list/132
[2025-08-20 17:35:06.459102] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bc7fa3127a0745759e44af32150c86af GET /api/v1/knowledge 200 timecost=903.254
[2025-08-20 17:35:06.575400] [INFO process-70468-73996 elastic_transport._transport:349] - trace=ca92fb18438b42e1be8f8ab4da344ce9 GET http://172.20.20.220:9200/col_1755240839_4a901af3 [status:200 duration:0.058s]
[2025-08-20 17:35:06.624200] [INFO process-70468-73996 elastic_transport._transport:349] - trace=ca92fb18438b42e1be8f8ab4da344ce9 POST http://172.20.20.220:9200/col_1755240839_4a901af3/_refresh [status:200 duration:0.032s]
[2025-08-20 17:35:06.680303] [INFO process-70468-73996 elastic_transport._transport:349] - trace=ca92fb18438b42e1be8f8ab4da344ce9 POST http://172.20.20.220:9200/col_1755240839_4a901af3/_search [status:200 duration:0.040s]
[2025-08-20 17:35:06.704054] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ca92fb18438b42e1be8f8ab4da344ce9 GET /api/v1/knowledge/file_list/132 200 timecost=828.635
[2025-08-20 17:35:10.827012] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e9f8de35d3f14c82b907e115d8b0e57d GET /api/v1/llm/knowledge
[2025-08-20 17:35:10.831781] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f3f00d2cc69c4787adf3bad37e3107f9 GET /api/v1/llm
[2025-08-20 17:35:10.984086] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e9f8de35d3f14c82b907e115d8b0e57d GET /api/v1/llm/knowledge 200 timecost=157.074
[2025-08-20 17:35:11.006049] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f3f00d2cc69c4787adf3bad37e3107f9 GET /api/v1/llm 200 timecost=174.268
[2025-08-20 17:35:11.133517] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8704acd4121342e9ba9bb105c6b4ee80 GET /api/v1/knowledge
[2025-08-20 17:35:11.228841] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8704acd4121342e9ba9bb105c6b4ee80 GET /api/v1/knowledge 200 timecost=95.324
[2025-08-20 17:35:17.186024] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=995f03fb1660465793c79b85090f0462 GET /api/v1/knowledge
[2025-08-20 17:35:17.256434] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=995f03fb1660465793c79b85090f0462 GET /api/v1/knowledge 200 timecost=70.41
[2025-08-20 17:35:22.873219] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f93e1e056e09453b9430aacf14169a08 GET /api/v1/knowledge
[2025-08-20 17:35:22.947397] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f93e1e056e09453b9430aacf14169a08 GET /api/v1/knowledge 200 timecost=74.178
[2025-08-20 17:35:28.871250] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9e37513d01984f53abd4abf1e3b7d7fa GET /api/v1/knowledge
[2025-08-20 17:35:28.949384] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9e37513d01984f53abd4abf1e3b7d7fa GET /api/v1/knowledge 200 timecost=79.131
[2025-08-20 17:35:34.565305] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1762ed07f9144b8abb66ae495fb85ae9 GET /api/v1/knowledge
[2025-08-20 17:35:34.640677] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1762ed07f9144b8abb66ae495fb85ae9 GET /api/v1/knowledge 200 timecost=75.372
[2025-08-20 17:35:40.573290] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bcc49a9fa52b4f9ab5ac15b1110b0258 GET /api/v1/knowledge
[2025-08-20 17:35:40.669914] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bcc49a9fa52b4f9ab5ac15b1110b0258 GET /api/v1/knowledge 200 timecost=96.624
[2025-08-20 17:35:46.293837] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e603a78d63b245cf9905d98090b55ff5 GET /api/v1/knowledge
[2025-08-20 17:35:46.359778] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e603a78d63b245cf9905d98090b55ff5 GET /api/v1/knowledge 200 timecost=65.942
[2025-08-20 17:35:52.289480] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=039d95d279b24c989f8531c7e82a4cdb GET /api/v1/knowledge
[2025-08-20 17:35:52.361884] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=039d95d279b24c989f8531c7e82a4cdb GET /api/v1/knowledge 200 timecost=72.404
[2025-08-20 17:35:57.984026] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8da66c556f504026954c8d08e5e572fe GET /api/v1/knowledge
[2025-08-20 17:35:58.174897] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8da66c556f504026954c8d08e5e572fe GET /api/v1/knowledge 200 timecost=189.856
[2025-08-20 17:36:04.123009] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9c9a6d794b854f5d804ccd389223bfca GET /api/v1/knowledge
[2025-08-20 17:36:04.283538] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9c9a6d794b854f5d804ccd389223bfca GET /api/v1/knowledge 200 timecost=160.529
[2025-08-20 17:36:09.912814] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8a1fb0dfb9f64ae88b56aa9f2ce9e529 GET /api/v1/knowledge
[2025-08-20 17:36:10.018607] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8a1fb0dfb9f64ae88b56aa9f2ce9e529 GET /api/v1/knowledge 200 timecost=104.792
[2025-08-20 17:36:15.952785] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=dcd88670455d4f6a93ba5474c8797b18 GET /api/v1/knowledge
[2025-08-20 17:36:16.036581] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=dcd88670455d4f6a93ba5474c8797b18 GET /api/v1/knowledge 200 timecost=83.796
[2025-08-20 17:36:21.660221] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=71099efc29a84b10b2e3e32ad8e205da GET /api/v1/knowledge
[2025-08-20 17:36:21.735614] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=71099efc29a84b10b2e3e32ad8e205da GET /api/v1/knowledge 200 timecost=75.392
[2025-08-20 17:36:27.662675] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=968162a0157c4c63a87a547611698b5b GET /api/v1/knowledge
[2025-08-20 17:36:27.743809] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=968162a0157c4c63a87a547611698b5b GET /api/v1/knowledge 200 timecost=81.134
[2025-08-20 17:36:33.371409] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=861aedd7abaf4985898515c2010efe8d GET /api/v1/knowledge
[2025-08-20 17:36:33.445633] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=861aedd7abaf4985898515c2010efe8d GET /api/v1/knowledge 200 timecost=74.224
[2025-08-20 17:36:39.377634] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4b891d3ae9694c4593531ba7e116b176 GET /api/v1/knowledge
[2025-08-20 17:36:39.493914] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4b891d3ae9694c4593531ba7e116b176 GET /api/v1/knowledge 200 timecost=116.28
[2025-08-20 17:36:45.118688] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=355a3647551e4e76948b681dee4693b2 GET /api/v1/knowledge
[2025-08-20 17:36:45.198130] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=355a3647551e4e76948b681dee4693b2 GET /api/v1/knowledge 200 timecost=79.442
[2025-08-20 17:36:51.132689] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=db4525df90924ab2847aa9601d9cea94 GET /api/v1/knowledge
[2025-08-20 17:36:51.206758] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=db4525df90924ab2847aa9601d9cea94 GET /api/v1/knowledge 200 timecost=75.078
[2025-08-20 17:36:56.827267] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d340f947085342d58a6459554ef75211 GET /api/v1/knowledge
[2025-08-20 17:36:56.918044] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d340f947085342d58a6459554ef75211 GET /api/v1/knowledge 200 timecost=90.776
[2025-08-20 17:37:02.848727] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a994cf70d02847a5b10fc033a133194e GET /api/v1/knowledge
[2025-08-20 17:37:02.918437] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a994cf70d02847a5b10fc033a133194e GET /api/v1/knowledge 200 timecost=69.71
[2025-08-20 17:37:08.536664] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4955946ec334464cb9537cfc99b1c1f5 GET /api/v1/knowledge
[2025-08-20 17:37:08.613126] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4955946ec334464cb9537cfc99b1c1f5 GET /api/v1/knowledge 200 timecost=77.466
[2025-08-20 17:37:14.538543] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bf42afad300247c4a7b5faa4c5648d52 GET /api/v1/knowledge
[2025-08-20 17:37:14.614584] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bf42afad300247c4a7b5faa4c5648d52 GET /api/v1/knowledge 200 timecost=76.041
[2025-08-20 17:37:20.232274] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=341e5ec1251a48ceae5e0211740a7d17 GET /api/v1/knowledge
[2025-08-20 17:37:20.301120] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=341e5ec1251a48ceae5e0211740a7d17 GET /api/v1/knowledge 200 timecost=68.845
[2025-08-20 17:37:26.236774] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3757d5bce3ae481686cb8aa05bec5e1f GET /api/v1/knowledge
[2025-08-20 17:37:26.305132] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3757d5bce3ae481686cb8aa05bec5e1f GET /api/v1/knowledge 200 timecost=68.358
[2025-08-20 17:37:31.922235] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f408b563aab84a9caf7d16e67522d112 GET /api/v1/knowledge
[2025-08-20 17:37:31.989686] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f408b563aab84a9caf7d16e67522d112 GET /api/v1/knowledge 200 timecost=67.451
[2025-08-20 17:37:37.909761] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=600af8f2bc66493e9d98865b6120e54c GET /api/v1/knowledge
[2025-08-20 17:37:37.981948] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=600af8f2bc66493e9d98865b6120e54c GET /api/v1/knowledge 200 timecost=72.187
[2025-08-20 17:37:43.600050] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=641ef790d58e4039ad3ec3427b3fa01b GET /api/v1/knowledge
[2025-08-20 17:37:43.667315] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=641ef790d58e4039ad3ec3427b3fa01b GET /api/v1/knowledge 200 timecost=67.265
[2025-08-20 17:37:49.592405] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=303d9130f0a74290835180de1d0e234a GET /api/v1/knowledge
[2025-08-20 17:37:49.659109] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=303d9130f0a74290835180de1d0e234a GET /api/v1/knowledge 200 timecost=65.694
[2025-08-20 17:37:55.274987] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a3b13975b66344a2bac40c8e662e09ee GET /api/v1/knowledge
[2025-08-20 17:37:55.343231] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a3b13975b66344a2bac40c8e662e09ee GET /api/v1/knowledge 200 timecost=68.245
[2025-08-20 17:38:01.279815] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=982d3d92c99e4b0b8380910b23acf7d4 GET /api/v1/knowledge
[2025-08-20 17:38:01.347936] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=982d3d92c99e4b0b8380910b23acf7d4 GET /api/v1/knowledge 200 timecost=68.121
[2025-08-20 17:38:06.977242] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=286bf51be9254938bcdaf33f75197b91 GET /api/v1/knowledge
[2025-08-20 17:38:07.049211] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=286bf51be9254938bcdaf33f75197b91 GET /api/v1/knowledge 200 timecost=71.217
[2025-08-20 17:38:12.973076] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=14204d10fa5a4f16903b215c7e92f5fd GET /api/v1/knowledge
[2025-08-20 17:38:13.057161] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=14204d10fa5a4f16903b215c7e92f5fd GET /api/v1/knowledge 200 timecost=83.098
[2025-08-20 17:38:18.675331] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a1aff4ec43624c4cb5d682fcc7c12f5c GET /api/v1/knowledge
[2025-08-20 17:38:18.746573] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a1aff4ec43624c4cb5d682fcc7c12f5c GET /api/v1/knowledge 200 timecost=71.242
[2025-08-20 17:38:24.672460] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=aa1ae663604b43eb9e1cc6cf01384a91 GET /api/v1/knowledge
[2025-08-20 17:38:24.744680] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=aa1ae663604b43eb9e1cc6cf01384a91 GET /api/v1/knowledge 200 timecost=72.22
[2025-08-20 17:38:30.366735] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=368cf7eaf74c412ba07ce301e86a5b15 GET /api/v1/knowledge
[2025-08-20 17:38:30.433992] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=368cf7eaf74c412ba07ce301e86a5b15 GET /api/v1/knowledge 200 timecost=66.258
[2025-08-20 17:38:36.355065] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6722233aedb6436ea7387faa240df6a2 GET /api/v1/knowledge
[2025-08-20 17:38:36.421372] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6722233aedb6436ea7387faa240df6a2 GET /api/v1/knowledge 200 timecost=66.307
[2025-08-20 17:38:42.037168] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1d2c5c485247424594bb775ff448c049 GET /api/v1/knowledge
[2025-08-20 17:38:42.106456] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1d2c5c485247424594bb775ff448c049 GET /api/v1/knowledge 200 timecost=68.279
[2025-08-20 17:38:48.044384] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=36796122ad0642a6b41b75f5b67f85c4 GET /api/v1/knowledge
[2025-08-20 17:38:48.183959] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=36796122ad0642a6b41b75f5b67f85c4 GET /api/v1/knowledge 200 timecost=139.575
[2025-08-20 17:38:53.817482] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d749fd9313b44099bc23a4e3863d5e02 GET /api/v1/knowledge
[2025-08-20 17:38:53.937520] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d749fd9313b44099bc23a4e3863d5e02 GET /api/v1/knowledge 200 timecost=120.039
[2025-08-20 17:38:59.890059] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3bdebac5bf16452ba1473591144ccb58 GET /api/v1/knowledge
[2025-08-20 17:38:59.965082] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3bdebac5bf16452ba1473591144ccb58 GET /api/v1/knowledge 200 timecost=76.039
[2025-08-20 17:39:05.584715] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1b6c50e731da462da4c6b6deff11ecac GET /api/v1/knowledge
[2025-08-20 17:39:05.697840] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1b6c50e731da462da4c6b6deff11ecac GET /api/v1/knowledge 200 timecost=113.125
[2025-08-20 17:39:11.635746] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0bfa2f7ee95342a8b9970c94c026411f GET /api/v1/knowledge
[2025-08-20 17:39:11.708843] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0bfa2f7ee95342a8b9970c94c026411f GET /api/v1/knowledge 200 timecost=72.097
[2025-08-20 17:39:17.330619] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a46a3fd52069431d9e5ed29de4fd7398 GET /api/v1/knowledge
[2025-08-20 17:39:17.412815] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a46a3fd52069431d9e5ed29de4fd7398 GET /api/v1/knowledge 200 timecost=82.197
[2025-08-20 17:39:23.340869] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c0d49f7ed96c46c6988ebdb0b39c3459 GET /api/v1/knowledge
[2025-08-20 17:39:23.411225] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c0d49f7ed96c46c6988ebdb0b39c3459 GET /api/v1/knowledge 200 timecost=70.356
[2025-08-20 17:39:29.142613] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0542d04da4c84ed8b17bbbc7cf1796e9 GET /api/v1/knowledge
[2025-08-20 17:39:29.569995] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0542d04da4c84ed8b17bbbc7cf1796e9 GET /api/v1/knowledge 200 timecost=427.382
[2025-08-20 17:39:35.508849] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c52a64f73b394102a12128f8190ccf4c GET /api/v1/knowledge
[2025-08-20 17:39:35.577030] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c52a64f73b394102a12128f8190ccf4c GET /api/v1/knowledge 200 timecost=68.181
[2025-08-20 17:39:41.193958] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bef4abf12fe04194b7d731d7cf549271 GET /api/v1/knowledge
[2025-08-20 17:39:41.287145] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bef4abf12fe04194b7d731d7cf549271 GET /api/v1/knowledge 200 timecost=93.187
[2025-08-20 17:39:47.221133] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=aa429d1a901e4ee398cc1f299142add7 GET /api/v1/knowledge
[2025-08-20 17:39:47.290913] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=aa429d1a901e4ee398cc1f299142add7 GET /api/v1/knowledge 200 timecost=69.78
[2025-08-20 17:39:52.909013] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=404099e4ac9a45caa58fd3243cfb311a GET /api/v1/knowledge
[2025-08-20 17:39:53.023238] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=404099e4ac9a45caa58fd3243cfb311a GET /api/v1/knowledge 200 timecost=114.226
[2025-08-20 17:39:58.953699] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7eb836e3907c48409afb2ec692222646 GET /api/v1/knowledge
[2025-08-20 17:39:59.071337] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7eb836e3907c48409afb2ec692222646 GET /api/v1/knowledge 200 timecost=117.638
[2025-08-20 17:40:04.695749] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c0381145e8474dd390421dc59d1b9093 GET /api/v1/knowledge
[2025-08-20 17:40:04.767270] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c0381145e8474dd390421dc59d1b9093 GET /api/v1/knowledge 200 timecost=71.521
[2025-08-20 17:40:10.687716] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7cb56ffab018480f963b18c6357ff24e GET /api/v1/knowledge
[2025-08-20 17:40:10.759228] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7cb56ffab018480f963b18c6357ff24e GET /api/v1/knowledge 200 timecost=71.512
[2025-08-20 17:40:16.381055] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=78fbb927537a43749a66282d83e66a8d GET /api/v1/knowledge
[2025-08-20 17:40:16.453865] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=78fbb927537a43749a66282d83e66a8d GET /api/v1/knowledge 200 timecost=72.811
[2025-08-20 17:40:22.376115] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9ab5d873f1af4f76b5a5997bf47d0aaf GET /api/v1/knowledge
[2025-08-20 17:40:22.467585] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9ab5d873f1af4f76b5a5997bf47d0aaf GET /api/v1/knowledge 200 timecost=91.469
[2025-08-20 17:40:28.092943] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=58d6e6905a3942c09d6f416247844d21 GET /api/v1/knowledge
[2025-08-20 17:40:28.215168] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=58d6e6905a3942c09d6f416247844d21 GET /api/v1/knowledge 200 timecost=122.225
[2025-08-20 17:40:34.155755] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=578e629d45184cb3810979975e3708ba GET /api/v1/knowledge
[2025-08-20 17:40:34.239303] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=578e629d45184cb3810979975e3708ba GET /api/v1/knowledge 200 timecost=83.548
[2025-08-20 17:40:39.862574] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b0f2c02bd4944a3aaab2b2be1a2dbfad GET /api/v1/knowledge
[2025-08-20 17:40:40.008145] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b0f2c02bd4944a3aaab2b2be1a2dbfad GET /api/v1/knowledge 200 timecost=145.571
[2025-08-20 17:40:45.946242] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d28e9e3cd7d54883b8ab40b94b3ce51f GET /api/v1/knowledge
[2025-08-20 17:40:46.018864] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d28e9e3cd7d54883b8ab40b94b3ce51f GET /api/v1/knowledge 200 timecost=72.622
[2025-08-20 17:40:51.636103] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b81e2426e95d4e39b7818607ab356ca3 GET /api/v1/knowledge
[2025-08-20 17:40:51.704139] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b81e2426e95d4e39b7818607ab356ca3 GET /api/v1/knowledge 200 timecost=68.036
[2025-08-20 17:40:57.633740] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1563da9b0a484868be6d915a77166387 GET /api/v1/knowledge
[2025-08-20 17:40:57.700096] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1563da9b0a484868be6d915a77166387 GET /api/v1/knowledge 200 timecost=66.356
[2025-08-20 17:41:03.315226] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b3d2d00bc0934047b56b4394f8391cff GET /api/v1/knowledge
[2025-08-20 17:41:03.415156] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b3d2d00bc0934047b56b4394f8391cff GET /api/v1/knowledge 200 timecost=99.2
[2025-08-20 17:41:09.347181] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ca53506183b54e2ebf10d708c7f5a420 GET /api/v1/knowledge
[2025-08-20 17:41:09.412913] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ca53506183b54e2ebf10d708c7f5a420 GET /api/v1/knowledge 200 timecost=65.732
[2025-08-20 17:41:15.029130] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=361e1034600f4084b08339d0018efe01 GET /api/v1/knowledge
[2025-08-20 17:41:15.112395] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=361e1034600f4084b08339d0018efe01 GET /api/v1/knowledge 200 timecost=83.265
[2025-08-20 17:41:21.046174] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2ca312429d4744938606d22c6540f0bb GET /api/v1/knowledge
[2025-08-20 17:41:21.161005] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2ca312429d4744938606d22c6540f0bb GET /api/v1/knowledge 200 timecost=113.821
[2025-08-20 17:41:26.787409] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=770d292e475f4cf69f4fb55dd47fe265 GET /api/v1/knowledge
[2025-08-20 17:41:26.993072] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=770d292e475f4cf69f4fb55dd47fe265 GET /api/v1/knowledge 200 timecost=204.665
[2025-08-20 17:41:32.937346] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=79cb855ab0fb427683ba0377cb0ff575 GET /api/v1/knowledge
[2025-08-20 17:41:33.065005] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=79cb855ab0fb427683ba0377cb0ff575 GET /api/v1/knowledge 200 timecost=127.659
[2025-08-20 17:41:38.686931] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=327e311c0f544b59b5c1cea1a2adb62f GET /api/v1/knowledge
[2025-08-20 17:41:38.792565] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=327e311c0f544b59b5c1cea1a2adb62f GET /api/v1/knowledge 200 timecost=105.634
[2025-08-20 17:41:44.723208] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=885208ebba2c4fbc993f1a2e439d180a GET /api/v1/knowledge
[2025-08-20 17:41:44.819774] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=885208ebba2c4fbc993f1a2e439d180a GET /api/v1/knowledge 200 timecost=96.566
[2025-08-20 17:41:50.439130] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=5e157c7dbfdd44a89d32fe256d68c778 GET /api/v1/knowledge
[2025-08-20 17:41:50.512668] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=5e157c7dbfdd44a89d32fe256d68c778 GET /api/v1/knowledge 200 timecost=73.538
[2025-08-20 17:41:56.443970] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2aea06ffa65441d39510b3033bdda863 GET /api/v1/knowledge
[2025-08-20 17:41:56.547006] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2aea06ffa65441d39510b3033bdda863 GET /api/v1/knowledge 200 timecost=103.543
[2025-08-20 17:42:02.171954] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=94f2fbb21d63491abfc3691f83692028 GET /api/v1/knowledge
[2025-08-20 17:42:02.247275] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=94f2fbb21d63491abfc3691f83692028 GET /api/v1/knowledge 200 timecost=75.322
[2025-08-20 17:42:08.180033] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=aaedd1369b994f3d98bad5a0cb5e35b4 GET /api/v1/knowledge
[2025-08-20 17:42:08.264720] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=aaedd1369b994f3d98bad5a0cb5e35b4 GET /api/v1/knowledge 200 timecost=84.687
[2025-08-20 17:42:13.884795] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9c3924f5f0004d1aba7725f5cb1900cb GET /api/v1/knowledge
[2025-08-20 17:42:13.963375] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9c3924f5f0004d1aba7725f5cb1900cb GET /api/v1/knowledge 200 timecost=78.58
[2025-08-20 17:42:19.890067] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8aee6d863cb74ad6a37bbd7c24f89bdd GET /api/v1/knowledge
[2025-08-20 17:42:19.997263] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8aee6d863cb74ad6a37bbd7c24f89bdd GET /api/v1/knowledge 200 timecost=107.197
[2025-08-20 17:42:25.624383] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0b78a0f66cb244f8946c223b90788611 GET /api/v1/knowledge
[2025-08-20 17:42:25.693368] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0b78a0f66cb244f8946c223b90788611 GET /api/v1/knowledge 200 timecost=68.985
[2025-08-20 17:42:31.623476] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b10b0b91b42d41eca071bd3c03faf595 GET /api/v1/knowledge
[2025-08-20 17:42:31.764433] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b10b0b91b42d41eca071bd3c03faf595 GET /api/v1/knowledge 200 timecost=140.957
[2025-08-20 17:42:37.390227] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=fb93650a8a9d4f8a9bcb45e7bdd960c0 GET /api/v1/knowledge
[2025-08-20 17:42:37.494235] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=fb93650a8a9d4f8a9bcb45e7bdd960c0 GET /api/v1/knowledge 200 timecost=103.032
[2025-08-20 17:42:43.429797] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0bfb12158f134675bde6259cc4bbd44f GET /api/v1/knowledge
[2025-08-20 17:42:43.515076] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0bfb12158f134675bde6259cc4bbd44f GET /api/v1/knowledge 200 timecost=85.279
[2025-08-20 17:42:49.131981] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a156ae856f824d3eaf58a707f562ef62 GET /api/v1/knowledge
[2025-08-20 17:42:49.199167] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a156ae856f824d3eaf58a707f562ef62 GET /api/v1/knowledge 200 timecost=67.185
[2025-08-20 17:42:55.121575] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a9017cceb032436d8c6ed42f90098078 GET /api/v1/knowledge
[2025-08-20 17:42:55.238459] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a9017cceb032436d8c6ed42f90098078 GET /api/v1/knowledge 200 timecost=116.884
[2025-08-20 17:43:00.866798] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d843cd95721643b2a143471fea4e434d GET /api/v1/knowledge
[2025-08-20 17:43:00.954487] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d843cd95721643b2a143471fea4e434d GET /api/v1/knowledge 200 timecost=87.689
[2025-08-20 17:43:06.881735] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1bfe3a98b8c74220a750970c662dd397 GET /api/v1/knowledge
[2025-08-20 17:43:06.984725] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1bfe3a98b8c74220a750970c662dd397 GET /api/v1/knowledge 200 timecost=102.989
[2025-08-20 17:43:12.614590] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f7caa3ba4b1744ceb7a55feac915bf33 GET /api/v1/knowledge
[2025-08-20 17:43:12.925249] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f7caa3ba4b1744ceb7a55feac915bf33 GET /api/v1/knowledge 200 timecost=311.662
[2025-08-20 17:43:18.880397] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=217aff0be5fa493394cf684eb2b7a1b8 GET /api/v1/knowledge
[2025-08-20 17:43:19.029100] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=217aff0be5fa493394cf684eb2b7a1b8 GET /api/v1/knowledge 200 timecost=148.703
[2025-08-20 17:43:24.657930] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3825d582f9d44977851afa62da0592a5 GET /api/v1/knowledge
[2025-08-20 17:43:24.732127] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3825d582f9d44977851afa62da0592a5 GET /api/v1/knowledge 200 timecost=74.198
[2025-08-20 17:43:30.657264] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=44c0dec3e7084a8f90d27e1a036e128f GET /api/v1/knowledge
[2025-08-20 17:43:30.735421] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=44c0dec3e7084a8f90d27e1a036e128f GET /api/v1/knowledge 200 timecost=78.156
[2025-08-20 17:43:36.353269] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3718190b3a1c4845aa91bc9de701a5f5 GET /api/v1/knowledge
[2025-08-20 17:43:36.438921] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3718190b3a1c4845aa91bc9de701a5f5 GET /api/v1/knowledge 200 timecost=85.652
[2025-08-20 17:43:42.365518] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=78211dc0837943aa9f2ad0f2fb919126 GET /api/v1/knowledge
[2025-08-20 17:43:42.570806] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=78211dc0837943aa9f2ad0f2fb919126 GET /api/v1/knowledge 200 timecost=205.288
[2025-08-20 17:43:48.206015] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3042079f1c0c4a109f8e20e72b866f2b GET /api/v1/knowledge
[2025-08-20 17:43:48.406155] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3042079f1c0c4a109f8e20e72b866f2b GET /api/v1/knowledge 200 timecost=200.14
[2025-08-20 17:43:54.344372] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e8f87a95794948ecae9ff1de23b76e55 GET /api/v1/knowledge
[2025-08-20 17:43:54.519303] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e8f87a95794948ecae9ff1de23b76e55 GET /api/v1/knowledge 200 timecost=174.931
[2025-08-20 17:44:00.145236] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=fd40c153cbc14a4fb662ddb28cc3e188 GET /api/v1/knowledge
[2025-08-20 17:44:00.244476] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=fd40c153cbc14a4fb662ddb28cc3e188 GET /api/v1/knowledge 200 timecost=100.239
[2025-08-20 17:44:06.176212] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=817979b2f22740fb9703e5cfdf5b82ff GET /api/v1/knowledge
[2025-08-20 17:44:06.342642] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=817979b2f22740fb9703e5cfdf5b82ff GET /api/v1/knowledge 200 timecost=167.034
[2025-08-20 17:44:11.967533] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=43545653a50146da8b19899bb11b1bd1 GET /api/v1/knowledge
[2025-08-20 17:44:12.047008] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=43545653a50146da8b19899bb11b1bd1 GET /api/v1/knowledge 200 timecost=79.475
[2025-08-20 17:44:17.981951] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6f98fa05130745fd8a337f25a5677981 GET /api/v1/knowledge
[2025-08-20 17:44:18.070843] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6f98fa05130745fd8a337f25a5677981 GET /api/v1/knowledge 200 timecost=87.881
[2025-08-20 17:44:23.690943] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a1d18a3dfc2b4f349c71d10c94af59d9 GET /api/v1/knowledge
[2025-08-20 17:44:23.792780] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a1d18a3dfc2b4f349c71d10c94af59d9 GET /api/v1/knowledge 200 timecost=101.836
[2025-08-20 17:44:29.725715] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a00ea82ce7264fafbc5ce51c5d1d6bc5 GET /api/v1/knowledge
[2025-08-20 17:44:29.926762] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a00ea82ce7264fafbc5ce51c5d1d6bc5 GET /api/v1/knowledge 200 timecost=201.047
[2025-08-20 17:44:35.562796] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=690ab66c26af436696868c53e1a60728 GET /api/v1/knowledge
[2025-08-20 17:44:35.681838] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=690ab66c26af436696868c53e1a60728 GET /api/v1/knowledge 200 timecost=119.042
[2025-08-20 17:44:41.621286] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c0098c072a7447ebb04fb50e6a14498b GET /api/v1/knowledge
[2025-08-20 17:44:41.995173] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c0098c072a7447ebb04fb50e6a14498b GET /api/v1/knowledge 200 timecost=373.886
[2025-08-20 17:44:47.636257] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d390f9bc1c544233bd7f5f641f8ff97c GET /api/v1/knowledge
[2025-08-20 17:44:47.811278] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d390f9bc1c544233bd7f5f641f8ff97c GET /api/v1/knowledge 200 timecost=175.021
[2025-08-20 17:44:53.752103] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9144089020e544139a8207931dfe33ce GET /api/v1/knowledge
[2025-08-20 17:44:53.848207] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9144089020e544139a8207931dfe33ce GET /api/v1/knowledge 200 timecost=96.104
[2025-08-20 17:44:59.475143] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ac2cf78412cc4e7682056fa567812801 GET /api/v1/knowledge
[2025-08-20 17:44:59.586361] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ac2cf78412cc4e7682056fa567812801 GET /api/v1/knowledge 200 timecost=111.218
[2025-08-20 17:45:05.521739] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=094e30ef561a48029f03037695c5ab74 GET /api/v1/knowledge
[2025-08-20 17:45:05.654314] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=094e30ef561a48029f03037695c5ab74 GET /api/v1/knowledge 200 timecost=132.575
[2025-08-20 17:45:11.278571] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=952d062037fa4696af8838ab9719bfa3 GET /api/v1/knowledge
[2025-08-20 17:45:11.403083] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=952d062037fa4696af8838ab9719bfa3 GET /api/v1/knowledge 200 timecost=124.512
[2025-08-20 17:45:17.333576] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2432fb5fa45c4184bbe57e29d55a2de3 GET /api/v1/knowledge
[2025-08-20 17:45:17.428415] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2432fb5fa45c4184bbe57e29d55a2de3 GET /api/v1/knowledge 200 timecost=94.839
[2025-08-20 17:45:23.046055] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2b3a8cd6919540c7a475c9e555472ecb GET /api/v1/knowledge
[2025-08-20 17:45:23.128540] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2b3a8cd6919540c7a475c9e555472ecb GET /api/v1/knowledge 200 timecost=82.486
[2025-08-20 17:45:29.192310] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d3990eda2bbe4b6f833da000c61745fa GET /api/v1/knowledge
[2025-08-20 17:45:29.648647] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d3990eda2bbe4b6f833da000c61745fa GET /api/v1/knowledge 200 timecost=456.337
[2025-08-20 17:45:35.403446] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b5359c73b5df4e32a5ea9f9db1c3189c GET /api/v1/knowledge
[2025-08-20 17:45:35.585169] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b5359c73b5df4e32a5ea9f9db1c3189c GET /api/v1/knowledge 200 timecost=181.723
[2025-08-20 17:45:41.527805] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=87ef65bcd8a243c99a253bbb3db710f4 GET /api/v1/knowledge
[2025-08-20 17:45:41.639870] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=87ef65bcd8a243c99a253bbb3db710f4 GET /api/v1/knowledge 200 timecost=113.066
[2025-08-20 17:45:47.261544] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1da7948df1524e4a8122397946b9eae6 GET /api/v1/knowledge
[2025-08-20 17:45:47.368037] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1da7948df1524e4a8122397946b9eae6 GET /api/v1/knowledge 200 timecost=105.473
[2025-08-20 17:45:53.311786] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d5de95cff07f4c759a0b7714e161a06c GET /api/v1/knowledge
[2025-08-20 17:45:53.421700] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d5de95cff07f4c759a0b7714e161a06c GET /api/v1/knowledge 200 timecost=109.914
[2025-08-20 17:45:59.039747] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b7d0a963bcb2451ca1ad99e68fc817a9 GET /api/v1/knowledge
[2025-08-20 17:45:59.125259] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b7d0a963bcb2451ca1ad99e68fc817a9 GET /api/v1/knowledge 200 timecost=85.512
[2025-08-20 17:46:05.050133] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0148dbc52c844c999ea77630019310d0 GET /api/v1/knowledge
[2025-08-20 17:46:05.154961] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0148dbc52c844c999ea77630019310d0 GET /api/v1/knowledge 200 timecost=104.828
[2025-08-20 17:46:10.776754] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e5d126dfb31a43a294ac472361d480fc GET /api/v1/knowledge
[2025-08-20 17:46:10.943316] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e5d126dfb31a43a294ac472361d480fc GET /api/v1/knowledge 200 timecost=166.562
[2025-08-20 17:46:16.876885] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ea8a80a55c4840e8be25df676cfc9214 GET /api/v1/knowledge
[2025-08-20 17:46:16.988493] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ea8a80a55c4840e8be25df676cfc9214 GET /api/v1/knowledge 200 timecost=111.607
[2025-08-20 17:46:22.606554] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=16474ad94a8f4f19b0c68b3664df7ea4 GET /api/v1/knowledge
[2025-08-20 17:46:22.688066] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=16474ad94a8f4f19b0c68b3664df7ea4 GET /api/v1/knowledge 200 timecost=81.512
[2025-08-20 17:46:28.619046] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a66845e1a197477292c1c5214b7d8b08 GET /api/v1/knowledge
[2025-08-20 17:46:28.716735] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a66845e1a197477292c1c5214b7d8b08 GET /api/v1/knowledge 200 timecost=97.69
[2025-08-20 17:46:34.333518] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=84e30596bb594eeb9f3c31a73f44d080 GET /api/v1/knowledge
[2025-08-20 17:46:34.467796] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=84e30596bb594eeb9f3c31a73f44d080 GET /api/v1/knowledge 200 timecost=135.278
[2025-08-20 17:46:40.405489] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=fbbc57e174b84db0948ffc306b6f9b55 GET /api/v1/knowledge
[2025-08-20 17:46:40.549855] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=fbbc57e174b84db0948ffc306b6f9b55 GET /api/v1/knowledge 200 timecost=144.365
[2025-08-20 17:46:46.173576] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c230cd1b843f40c38bcb469319104108 GET /api/v1/knowledge
[2025-08-20 17:46:46.296361] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c230cd1b843f40c38bcb469319104108 GET /api/v1/knowledge 200 timecost=122.786
[2025-08-20 17:46:52.223318] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=44fa87e95b1645b9a7c1a7ad4e8a3457 GET /api/v1/knowledge
[2025-08-20 17:46:52.394588] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=44fa87e95b1645b9a7c1a7ad4e8a3457 GET /api/v1/knowledge 200 timecost=171.27
[2025-08-20 17:46:58.029060] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=5de853559d0c409692decc1f960ffde6 GET /api/v1/knowledge
[2025-08-20 17:46:58.316357] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=5de853559d0c409692decc1f960ffde6 GET /api/v1/knowledge 200 timecost=286.298
[2025-08-20 17:47:04.258333] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ca571873bf5a404bb27c29df08dfaeb6 GET /api/v1/knowledge
[2025-08-20 17:47:04.423365] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ca571873bf5a404bb27c29df08dfaeb6 GET /api/v1/knowledge 200 timecost=166.044
[2025-08-20 17:47:10.057654] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=dd03147d611a4a2eb5c2df06a866988f GET /api/v1/knowledge
[2025-08-20 17:47:10.190523] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=dd03147d611a4a2eb5c2df06a866988f GET /api/v1/knowledge 200 timecost=133.62
[2025-08-20 17:47:16.121209] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=10667d9728ff474685b7f91cf21563b6 GET /api/v1/knowledge
[2025-08-20 17:47:16.270404] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=10667d9728ff474685b7f91cf21563b6 GET /api/v1/knowledge 200 timecost=149.195
[2025-08-20 17:47:21.889532] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7116b7189f5b475b834081f99502768a GET /api/v1/knowledge
[2025-08-20 17:47:22.017313] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7116b7189f5b475b834081f99502768a GET /api/v1/knowledge 200 timecost=127.781
[2025-08-20 17:47:27.950835] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=add62942655349bdb56d1ea722db0abd GET /api/v1/knowledge
[2025-08-20 17:47:28.035240] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=add62942655349bdb56d1ea722db0abd GET /api/v1/knowledge 200 timecost=84.405
[2025-08-20 17:47:33.656204] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=71cb4d5e82eb46838c7b9685749ce62e GET /api/v1/knowledge
[2025-08-20 17:47:33.742859] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=71cb4d5e82eb46838c7b9685749ce62e GET /api/v1/knowledge 200 timecost=87.052
[2025-08-20 17:47:39.674303] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e07adf9c986141189b96318e38c7fdca GET /api/v1/knowledge
[2025-08-20 17:47:39.764602] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e07adf9c986141189b96318e38c7fdca GET /api/v1/knowledge 200 timecost=90.298
[2025-08-20 17:47:45.382921] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=5e73c9b02da24a53886679a629171cb1 GET /api/v1/knowledge
[2025-08-20 17:47:45.466228] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=5e73c9b02da24a53886679a629171cb1 GET /api/v1/knowledge 200 timecost=83.307
[2025-08-20 17:47:51.392545] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=19f582e974974812b05462bc5ca832ad GET /api/v1/knowledge
[2025-08-20 17:47:51.469650] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=19f582e974974812b05462bc5ca832ad GET /api/v1/knowledge 200 timecost=77.105
[2025-08-20 17:47:57.093562] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=15ba5ce464ee4177adaa273045f4e870 GET /api/v1/knowledge
[2025-08-20 17:47:57.204664] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=15ba5ce464ee4177adaa273045f4e870 GET /api/v1/knowledge 200 timecost=111.102
[2025-08-20 17:48:03.138789] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=5919d3dbcb80473782e574e3c7af67c6 GET /api/v1/knowledge
[2025-08-20 17:48:03.289020] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=5919d3dbcb80473782e574e3c7af67c6 GET /api/v1/knowledge 200 timecost=150.232
[2025-08-20 17:48:08.918063] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9ed065d7c66742b483ed94ab5de2b062 GET /api/v1/knowledge
[2025-08-20 17:48:09.149023] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9ed065d7c66742b483ed94ab5de2b062 GET /api/v1/knowledge 200 timecost=230.96
[2025-08-20 17:48:15.118276] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a99f10234c0b498b9984efebe95353a5 GET /api/v1/knowledge
[2025-08-20 17:48:15.491790] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a99f10234c0b498b9984efebe95353a5 GET /api/v1/knowledge 200 timecost=373.513
[2025-08-20 17:48:21.163911] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=889f2d629a4c417ea319a3b2f7c6a088 GET /api/v1/knowledge
[2025-08-20 17:48:21.509027] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=889f2d629a4c417ea319a3b2f7c6a088 GET /api/v1/knowledge 200 timecost=345.116
[2025-08-20 17:48:27.470988] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e2b5bace25f34c29bb3a90b3e0f9bd0e GET /api/v1/knowledge
[2025-08-20 17:48:27.752681] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e2b5bace25f34c29bb3a90b3e0f9bd0e GET /api/v1/knowledge 200 timecost=281.693
[2025-08-20 17:48:33.403225] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a9a4df231e0f482297f0aed9381cc555 GET /api/v1/knowledge
[2025-08-20 17:48:33.621113] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a9a4df231e0f482297f0aed9381cc555 GET /api/v1/knowledge 200 timecost=217.888
[2025-08-20 17:48:39.586059] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a76401ee8fb148379ed2be1b763ae786 GET /api/v1/knowledge
[2025-08-20 17:48:39.848624] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a76401ee8fb148379ed2be1b763ae786 GET /api/v1/knowledge 200 timecost=262.565
[2025-08-20 17:48:45.489777] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4496d4cc3f7f49c288f590cb7e9a1f89 GET /api/v1/knowledge
[2025-08-20 17:48:45.719734] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4496d4cc3f7f49c288f590cb7e9a1f89 GET /api/v1/knowledge 200 timecost=228.959
[2025-08-20 17:48:51.667424] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0bd87f16e3ef4cb1bf25c493574126ee GET /api/v1/knowledge
[2025-08-20 17:48:51.768220] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0bd87f16e3ef4cb1bf25c493574126ee GET /api/v1/knowledge 200 timecost=100.797
[2025-08-20 17:48:57.400054] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=27c82172765e4078b83258147198d018 GET /api/v1/knowledge
[2025-08-20 17:48:57.611993] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=27c82172765e4078b83258147198d018 GET /api/v1/knowledge 200 timecost=211.939
[2025-08-20 17:49:03.564042] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6c639d0a43884fcaa91730d4264e43f6 GET /api/v1/knowledge
[2025-08-20 17:49:03.660081] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6c639d0a43884fcaa91730d4264e43f6 GET /api/v1/knowledge 200 timecost=96.038
[2025-08-20 17:49:09.279737] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=66473e2185d44432bcbf5fa9d5deb8e0 GET /api/v1/knowledge
[2025-08-20 17:49:09.377138] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=66473e2185d44432bcbf5fa9d5deb8e0 GET /api/v1/knowledge 200 timecost=97.401
[2025-08-20 17:49:15.304267] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b289e57f58d94b81921ea41eca224129 GET /api/v1/knowledge
[2025-08-20 17:49:15.384228] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b289e57f58d94b81921ea41eca224129 GET /api/v1/knowledge 200 timecost=79.961
[2025-08-20 17:49:21.016190] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8095f5ac5c4e4b7299b7b038d033db7e GET /api/v1/knowledge
[2025-08-20 17:49:21.272692] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8095f5ac5c4e4b7299b7b038d033db7e GET /api/v1/knowledge 200 timecost=256.501
[2025-08-20 17:49:27.256497] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=722eb66c3938407ebc538b9c95824873 GET /api/v1/knowledge
[2025-08-20 17:49:27.395105] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=722eb66c3938407ebc538b9c95824873 GET /api/v1/knowledge 200 timecost=137.614
[2025-08-20 17:49:33.021658] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=105b93b0610743fc9b099c5e911bc06d GET /api/v1/knowledge
[2025-08-20 17:49:33.140357] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=105b93b0610743fc9b099c5e911bc06d GET /api/v1/knowledge 200 timecost=118.699
[2025-08-20 17:49:39.082720] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2e9c6a6e555b47ed9a8b97d4a01c82a0 GET /api/v1/knowledge
[2025-08-20 17:49:39.222771] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2e9c6a6e555b47ed9a8b97d4a01c82a0 GET /api/v1/knowledge 200 timecost=140.051
[2025-08-20 17:49:44.860019] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=724653b109124eaf8916bb32a0a3afca GET /api/v1/knowledge
[2025-08-20 17:49:45.118024] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=724653b109124eaf8916bb32a0a3afca GET /api/v1/knowledge 200 timecost=258.004
[2025-08-20 17:49:51.067682] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3a8aae5819cf4b62a7bbfdadc3c775ed GET /api/v1/knowledge
[2025-08-20 17:49:51.208677] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3a8aae5819cf4b62a7bbfdadc3c775ed GET /api/v1/knowledge 200 timecost=140.995
[2025-08-20 17:49:56.838227] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=af510178c58f4a74b62534eda7419ae8 GET /api/v1/knowledge
[2025-08-20 17:49:57.081672] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=af510178c58f4a74b62534eda7419ae8 GET /api/v1/knowledge 200 timecost=243.445
[2025-08-20 17:50:03.459433] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c12ec3ebdca249d48fe0c685e2e83984 GET /api/v1/knowledge
[2025-08-20 17:50:03.909011] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c12ec3ebdca249d48fe0c685e2e83984 GET /api/v1/knowledge 200 timecost=448.085
[2025-08-20 17:50:09.903061] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=986a60b10cf7458d971c8946e90adcd6 GET /api/v1/knowledge
[2025-08-20 17:50:10.039962] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=986a60b10cf7458d971c8946e90adcd6 GET /api/v1/knowledge 200 timecost=136.901
[2025-08-20 17:50:15.973457] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=5b648f6fbb69419e9c174a9464b4ca68 GET /api/v1/knowledge
[2025-08-20 17:50:16.273037] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=5b648f6fbb69419e9c174a9464b4ca68 GET /api/v1/knowledge 200 timecost=299.579
[2025-08-20 17:50:21.910980] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8db8d6a756354ddc92eb27a1982722ac GET /api/v1/knowledge
[2025-08-20 17:50:22.119324] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8db8d6a756354ddc92eb27a1982722ac GET /api/v1/knowledge 200 timecost=208.344
[2025-08-20 17:50:28.065451] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b99a20e3e799411cbf54b90183ba5a77 GET /api/v1/knowledge
[2025-08-20 17:50:28.203188] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b99a20e3e799411cbf54b90183ba5a77 GET /api/v1/knowledge 200 timecost=137.737
[2025-08-20 17:50:33.828808] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=32ae9894aca54ac0873036f60d31d534 GET /api/v1/knowledge
[2025-08-20 17:50:33.999815] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=32ae9894aca54ac0873036f60d31d534 GET /api/v1/knowledge 200 timecost=172.024
[2025-08-20 17:50:39.936196] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=35b09612556846c487c6dafc668fecf6 GET /api/v1/knowledge
[2025-08-20 17:50:40.156256] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=35b09612556846c487c6dafc668fecf6 GET /api/v1/knowledge 200 timecost=220.06
[2025-08-20 17:50:45.791926] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9a654c8f85c94500b8a8559c8b175bb5 GET /api/v1/knowledge
[2025-08-20 17:50:46.019119] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9a654c8f85c94500b8a8559c8b175bb5 GET /api/v1/knowledge 200 timecost=227.193
[2025-08-20 17:50:51.956461] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e0c2ed9807544242b1201e8c66c8c591 GET /api/v1/knowledge
[2025-08-20 17:50:52.094238] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e0c2ed9807544242b1201e8c66c8c591 GET /api/v1/knowledge 200 timecost=137.777
[2025-08-20 17:50:57.723643] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6e555d4d4fe34829861366ff7e0a18bd GET /api/v1/knowledge
[2025-08-20 17:50:57.868324] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6e555d4d4fe34829861366ff7e0a18bd GET /api/v1/knowledge 200 timecost=144.681
[2025-08-20 17:51:03.807295] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ddff105a105e42e2a6e32fe8c18dbfa5 GET /api/v1/knowledge
[2025-08-20 17:51:03.997263] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ddff105a105e42e2a6e32fe8c18dbfa5 GET /api/v1/knowledge 200 timecost=189.968
[2025-08-20 17:51:09.630193] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c69f34e4323b4f09a67b0ebd2dbe4834 GET /api/v1/knowledge
[2025-08-20 17:51:09.905658] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c69f34e4323b4f09a67b0ebd2dbe4834 GET /api/v1/knowledge 200 timecost=275.465
[2025-08-20 17:51:15.836460] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2ca54576413c4d74b57685adf5ecbf62 GET /api/v1/knowledge
[2025-08-20 17:51:16.005671] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2ca54576413c4d74b57685adf5ecbf62 GET /api/v1/knowledge 200 timecost=169.212
[2025-08-20 17:51:21.640308] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6a78c4d0fdb54216896f574972431210 GET /api/v1/knowledge
[2025-08-20 17:51:22.025884] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6a78c4d0fdb54216896f574972431210 GET /api/v1/knowledge 200 timecost=385.576
[2025-08-20 17:51:27.978666] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2f43a56731bc479eb7c0d09a7e8efac1 GET /api/v1/knowledge
[2025-08-20 17:51:28.186931] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2f43a56731bc479eb7c0d09a7e8efac1 GET /api/v1/knowledge 200 timecost=208.266
[2025-08-20 17:51:33.815622] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1fa5a7eee84c4915b2ffd2eef125268f GET /api/v1/knowledge
[2025-08-20 17:51:34.032449] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1fa5a7eee84c4915b2ffd2eef125268f GET /api/v1/knowledge 200 timecost=216.826
[2025-08-20 17:51:39.974296] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=fe74b1ca40c3448db850d95d56ddd41f GET /api/v1/knowledge
[2025-08-20 17:51:40.163798] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=fe74b1ca40c3448db850d95d56ddd41f GET /api/v1/knowledge 200 timecost=189.502
[2025-08-20 17:51:45.794338] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=51a2d803a3f64b63985c70ea4d228200 GET /api/v1/knowledge
[2025-08-20 17:51:46.022412] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=51a2d803a3f64b63985c70ea4d228200 GET /api/v1/knowledge 200 timecost=228.074
[2025-08-20 17:51:51.962668] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8a985c27bbce4875a7de015f88f93124 GET /api/v1/knowledge
[2025-08-20 17:51:52.119096] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8a985c27bbce4875a7de015f88f93124 GET /api/v1/knowledge 200 timecost=155.427
[2025-08-20 17:51:57.743895] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=aa5904b44ada49f09edbbabc1c9d985a GET /api/v1/knowledge
[2025-08-20 17:51:57.997905] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=aa5904b44ada49f09edbbabc1c9d985a GET /api/v1/knowledge 200 timecost=255.013
[2025-08-20 17:52:03.937968] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=484629e9535d4dda82534847cd8b5ed1 GET /api/v1/knowledge
[2025-08-20 17:52:04.065392] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=484629e9535d4dda82534847cd8b5ed1 GET /api/v1/knowledge 200 timecost=127.425
[2025-08-20 17:52:09.686893] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=876376e9bb7b4d9a92c37c49321fa727 GET /api/v1/knowledge
[2025-08-20 17:52:09.793113] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=876376e9bb7b4d9a92c37c49321fa727 GET /api/v1/knowledge 200 timecost=106.221
[2025-08-20 17:52:15.716024] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=74634c297a184e7e93cc6a6e204edb57 GET /api/v1/knowledge
[2025-08-20 17:52:15.892188] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=74634c297a184e7e93cc6a6e204edb57 GET /api/v1/knowledge 200 timecost=177.163
[2025-08-20 17:52:21.517331] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=223a70d716a34a489d4d9ddaf472e1f3 GET /api/v1/knowledge
[2025-08-20 17:52:21.628268] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=223a70d716a34a489d4d9ddaf472e1f3 GET /api/v1/knowledge 200 timecost=110.937
[2025-08-20 17:52:27.561938] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e7d89967e3e843d185cb10f2e70ae3a6 GET /api/v1/knowledge
[2025-08-20 17:52:27.699995] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e7d89967e3e843d185cb10f2e70ae3a6 GET /api/v1/knowledge 200 timecost=138.057
[2025-08-20 17:52:33.315203] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bc3923071b7b492d919a5a2e01428654 GET /api/v1/knowledge
[2025-08-20 17:52:33.437961] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bc3923071b7b492d919a5a2e01428654 GET /api/v1/knowledge 200 timecost=122.757
[2025-08-20 17:52:39.361101] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d57fb2a352144b76af48b236c656f07b GET /api/v1/knowledge
[2025-08-20 17:52:39.467406] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d57fb2a352144b76af48b236c656f07b GET /api/v1/knowledge 200 timecost=105.291
[2025-08-20 17:52:45.085969] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=19cdf4b2422640039fa652882508cda3 GET /api/v1/knowledge
[2025-08-20 17:52:45.199859] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=19cdf4b2422640039fa652882508cda3 GET /api/v1/knowledge 200 timecost=113.89
[2025-08-20 17:52:51.127907] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a270a512b55c4db18a9f5121ec8574d9 GET /api/v1/knowledge
[2025-08-20 17:52:51.326880] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a270a512b55c4db18a9f5121ec8574d9 GET /api/v1/knowledge 200 timecost=198.973
[2025-08-20 17:52:56.956728] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=edbab5b754a34ed99a5132c50a52bd95 GET /api/v1/knowledge
[2025-08-20 17:52:57.102761] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=edbab5b754a34ed99a5132c50a52bd95 GET /api/v1/knowledge 200 timecost=146.033
[2025-08-20 17:53:03.043900] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d8f2d6bfa2e2412e9840b100eb0845a8 GET /api/v1/knowledge
[2025-08-20 17:53:03.311484] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d8f2d6bfa2e2412e9840b100eb0845a8 GET /api/v1/knowledge 200 timecost=268.581
[2025-08-20 17:53:08.940032] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9eaca2f9c89c4b1f909b415f4b4e30c0 GET /api/v1/knowledge
[2025-08-20 17:53:09.147770] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9eaca2f9c89c4b1f909b415f4b4e30c0 GET /api/v1/knowledge 200 timecost=207.738
[2025-08-20 17:53:15.089038] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=46ba18e403774890bdd11c45e849f8d8 GET /api/v1/knowledge
[2025-08-20 17:53:15.198617] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=46ba18e403774890bdd11c45e849f8d8 GET /api/v1/knowledge 200 timecost=109.578
[2025-08-20 17:53:20.830769] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=066bac4de92844d7b328853255a3bdf9 GET /api/v1/knowledge
[2025-08-20 17:53:20.971685] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=066bac4de92844d7b328853255a3bdf9 GET /api/v1/knowledge 200 timecost=140.916
[2025-08-20 17:53:26.893596] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=027586fd0c35449d9649b6a656540c27 GET /api/v1/knowledge
[2025-08-20 17:53:26.986296] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=027586fd0c35449d9649b6a656540c27 GET /api/v1/knowledge 200 timecost=92.7
[2025-08-20 17:53:32.604436] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8f97a54738d7459fb89e1ee413f5fe50 GET /api/v1/knowledge
[2025-08-20 17:53:32.710303] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8f97a54738d7459fb89e1ee413f5fe50 GET /api/v1/knowledge 200 timecost=105.868
[2025-08-20 17:53:38.635721] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b2f743865fbc48418e8902d552c2dee1 GET /api/v1/knowledge
[2025-08-20 17:53:38.806396] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b2f743865fbc48418e8902d552c2dee1 GET /api/v1/knowledge 200 timecost=171.689
[2025-08-20 17:53:44.431416] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bcdb85dbdddc4c0b930d73b3419eb4b7 GET /api/v1/knowledge
[2025-08-20 17:53:44.554212] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bcdb85dbdddc4c0b930d73b3419eb4b7 GET /api/v1/knowledge 200 timecost=122.797
[2025-08-20 17:53:50.482691] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a9e55207ac7240ed9d809dde94ad58aa GET /api/v1/knowledge
[2025-08-20 17:53:50.606072] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a9e55207ac7240ed9d809dde94ad58aa GET /api/v1/knowledge 200 timecost=123.382
[2025-08-20 17:53:56.226735] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7740f04808d449d493a5bafa67e2e97f GET /api/v1/knowledge
[2025-08-20 17:53:56.355333] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7740f04808d449d493a5bafa67e2e97f GET /api/v1/knowledge 200 timecost=128.598
[2025-08-20 17:54:02.284520] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1b49f9cf7e374806b18b004bf0349288 GET /api/v1/knowledge
[2025-08-20 17:54:02.383903] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1b49f9cf7e374806b18b004bf0349288 GET /api/v1/knowledge 200 timecost=99.384
[2025-08-20 17:54:08.003332] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a801257b0c89497fa8b54f94d6e95096 GET /api/v1/knowledge
[2025-08-20 17:54:08.151963] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a801257b0c89497fa8b54f94d6e95096 GET /api/v1/knowledge 200 timecost=148.631
[2025-08-20 17:54:14.088366] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1bfb9acec7954bdcb64ed47ec98c441f GET /api/v1/knowledge
[2025-08-20 17:54:14.211884] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1bfb9acec7954bdcb64ed47ec98c441f GET /api/v1/knowledge 200 timecost=124.06
[2025-08-20 17:54:19.829857] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=804a7e10281c4b9eadc92d1e0e88b1d9 GET /api/v1/knowledge
[2025-08-20 17:54:19.942118] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=804a7e10281c4b9eadc92d1e0e88b1d9 GET /api/v1/knowledge 200 timecost=112.261
[2025-08-20 17:54:25.870389] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8638d33a52134d5ca71669960b559ce4 GET /api/v1/knowledge
[2025-08-20 17:54:25.962988] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8638d33a52134d5ca71669960b559ce4 GET /api/v1/knowledge 200 timecost=92.6
[2025-08-20 17:54:31.578521] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=91ed0a095cab427cb76bc61aad24c49a GET /api/v1/knowledge
[2025-08-20 17:54:31.707707] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=91ed0a095cab427cb76bc61aad24c49a GET /api/v1/knowledge 200 timecost=129.185
[2025-08-20 17:54:37.642409] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=cb531377e7f34cb9995a2c9ee2dc3a3b GET /api/v1/knowledge
[2025-08-20 17:54:37.765380] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=cb531377e7f34cb9995a2c9ee2dc3a3b GET /api/v1/knowledge 200 timecost=122.971
[2025-08-20 17:54:43.384624] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e6183afbc03640d180b63895863c2557 GET /api/v1/knowledge
[2025-08-20 17:54:43.496010] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e6183afbc03640d180b63895863c2557 GET /api/v1/knowledge 200 timecost=111.386
[2025-08-20 17:54:49.417982] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=435957389bb1456d9f0c9259ddf4a310 GET /api/v1/knowledge
[2025-08-20 17:54:49.545881] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=435957389bb1456d9f0c9259ddf4a310 GET /api/v1/knowledge 200 timecost=127.899
[2025-08-20 17:54:55.166836] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ec808830e3264a639402c75104deddf2 GET /api/v1/knowledge
[2025-08-20 17:54:55.278688] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ec808830e3264a639402c75104deddf2 GET /api/v1/knowledge 200 timecost=111.852
[2025-08-20 17:55:01.204506] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=630b26ab1ccf45beb2fe7c5632394b8d GET /api/v1/knowledge
[2025-08-20 17:55:01.389981] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=630b26ab1ccf45beb2fe7c5632394b8d GET /api/v1/knowledge 200 timecost=185.475
[2025-08-20 17:55:07.028651] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=eec7456a6dc2420686f00a6acedb9fef GET /api/v1/knowledge
[2025-08-20 17:55:07.231236] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=eec7456a6dc2420686f00a6acedb9fef GET /api/v1/knowledge 200 timecost=202.585
[2025-08-20 17:55:13.171321] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e8d013eb434442af8f16b0a1b01cd27b GET /api/v1/knowledge
[2025-08-20 17:55:13.252822] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e8d013eb434442af8f16b0a1b01cd27b GET /api/v1/knowledge 200 timecost=81.501
[2025-08-20 17:55:18.869172] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2c8fc8094f78456c85c15877be402ef6 GET /api/v1/knowledge
[2025-08-20 17:55:18.968078] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2c8fc8094f78456c85c15877be402ef6 GET /api/v1/knowledge 200 timecost=98.907
[2025-08-20 17:55:24.892023] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=450f05ba9caa4d499fd48ec6b848d94f GET /api/v1/knowledge
[2025-08-20 17:55:24.999978] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=450f05ba9caa4d499fd48ec6b848d94f GET /api/v1/knowledge 200 timecost=106.964
[2025-08-20 17:55:30.617406] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f6de36e6bd8c402cb97328c25df3c906 GET /api/v1/knowledge
[2025-08-20 17:55:30.707047] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f6de36e6bd8c402cb97328c25df3c906 GET /api/v1/knowledge 200 timecost=89.641
[2025-08-20 17:55:36.632067] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c1c7e48fb06143c19b97ae34247bd54d GET /api/v1/knowledge
[2025-08-20 17:55:36.814028] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c1c7e48fb06143c19b97ae34247bd54d GET /api/v1/knowledge 200 timecost=181.961
[2025-08-20 17:55:42.437104] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1c566b56fc174dcf97295fc7e90a4a7f GET /api/v1/knowledge
[2025-08-20 17:55:42.561467] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1c566b56fc174dcf97295fc7e90a4a7f GET /api/v1/knowledge 200 timecost=124.364
[2025-08-20 17:55:48.488079] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=dc0964be391a49caa1b06675bcd43206 GET /api/v1/knowledge
[2025-08-20 17:55:48.601240] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=dc0964be391a49caa1b06675bcd43206 GET /api/v1/knowledge 200 timecost=113.16
[2025-08-20 17:55:54.224747] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c4c54b9b32f34381926800010251334c GET /api/v1/knowledge
[2025-08-20 17:55:54.370767] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c4c54b9b32f34381926800010251334c GET /api/v1/knowledge 200 timecost=146.02
[2025-08-20 17:56:00.293672] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4c0bc6f17d06497990e84766e9f546d6 GET /api/v1/knowledge
[2025-08-20 17:56:00.438452] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4c0bc6f17d06497990e84766e9f546d6 GET /api/v1/knowledge 200 timecost=144.78
[2025-08-20 17:56:06.063896] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6b7d42cedd034100bb5b356ebb32982b GET /api/v1/knowledge
[2025-08-20 17:56:06.273333] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6b7d42cedd034100bb5b356ebb32982b GET /api/v1/knowledge 200 timecost=209.437
[2025-08-20 17:56:12.201722] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d1194289a76b42da81d88cf83ea8854a GET /api/v1/knowledge
[2025-08-20 17:56:12.348226] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d1194289a76b42da81d88cf83ea8854a GET /api/v1/knowledge 200 timecost=146.503
[2025-08-20 17:56:17.969248] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b141b12c547b4140b39096584c7ada69 GET /api/v1/knowledge
[2025-08-20 17:56:18.134841] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b141b12c547b4140b39096584c7ada69 GET /api/v1/knowledge 200 timecost=165.593
[2025-08-20 17:56:24.063091] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3c3eb9df9c1e4f24b6b28606d48bf26d GET /api/v1/knowledge
[2025-08-20 17:56:24.147031] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3c3eb9df9c1e4f24b6b28606d48bf26d GET /api/v1/knowledge 200 timecost=83.94
[2025-08-20 17:56:29.765592] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9b24b8cc621144b19cb3dc4eb0aadfab GET /api/v1/knowledge
[2025-08-20 17:56:29.858557] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9b24b8cc621144b19cb3dc4eb0aadfab GET /api/v1/knowledge 200 timecost=92.965
[2025-08-20 17:56:35.786284] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=073e177c2ebf40d4b158c32b0a4ac1de GET /api/v1/knowledge
[2025-08-20 17:56:35.859240] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=073e177c2ebf40d4b158c32b0a4ac1de GET /api/v1/knowledge 200 timecost=72.957
[2025-08-20 17:56:41.474727] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=23443d99ac6d4056b419d84c0fb060f4 GET /api/v1/knowledge
[2025-08-20 17:56:41.557723] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=23443d99ac6d4056b419d84c0fb060f4 GET /api/v1/knowledge 200 timecost=81.88
[2025-08-20 17:56:47.486883] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8ebbc37ed9b74b7daad45021acadf942 GET /api/v1/knowledge
[2025-08-20 17:56:47.560308] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8ebbc37ed9b74b7daad45021acadf942 GET /api/v1/knowledge 200 timecost=73.425
[2025-08-20 17:56:53.181099] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a16863f652c647e5a7fad1a563be099b GET /api/v1/knowledge
[2025-08-20 17:56:53.258401] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a16863f652c647e5a7fad1a563be099b GET /api/v1/knowledge 200 timecost=77.302
[2025-08-20 17:56:59.182820] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a36bb4a4ac4c409babdd1ca0b38832ad GET /api/v1/knowledge
[2025-08-20 17:56:59.269844] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a36bb4a4ac4c409babdd1ca0b38832ad GET /api/v1/knowledge 200 timecost=87.024
[2025-08-20 17:57:04.891378] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f29907387ef14a2c9925d421ccb27106 GET /api/v1/knowledge
[2025-08-20 17:57:04.974961] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f29907387ef14a2c9925d421ccb27106 GET /api/v1/knowledge 200 timecost=84.176
[2025-08-20 17:57:10.898957] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f3f03488c19f4f80bb06e2a0d8841e6d GET /api/v1/knowledge
[2025-08-20 17:57:10.970592] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f3f03488c19f4f80bb06e2a0d8841e6d GET /api/v1/knowledge 200 timecost=71.635
[2025-08-20 17:57:16.591397] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=abd093e7082b46e0b502137d824767ae GET /api/v1/knowledge
[2025-08-20 17:57:16.716330] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=abd093e7082b46e0b502137d824767ae GET /api/v1/knowledge 200 timecost=125.948
[2025-08-20 17:57:22.657557] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=62262714f303455a8ad52100efa5375d GET /api/v1/knowledge
[2025-08-20 17:57:23.021338] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=62262714f303455a8ad52100efa5375d GET /api/v1/knowledge 200 timecost=363.781
[2025-08-20 17:57:29.535845] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=11fa2cead75c43c89fc498fd563501a9 GET /api/v1/knowledge
[2025-08-20 17:57:29.846601] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=11fa2cead75c43c89fc498fd563501a9 GET /api/v1/knowledge 200 timecost=310.756
[2025-08-20 20:07:19.291730] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=551c8a9b494b4d7a805eaf284b58690b GET /api/v1/knowledge
[2025-08-20 20:07:40.354004] [ERROR process-70468-82156 __main__:33] - trace=1 GET http://127.0.0.1:7860/api/v1/knowledge?page_num=1&page_size=20&name=&type=0 Timeout connecting to server
[2025-08-20 20:09:04.185616] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=98136c54853a47bc8556c6675cea9b47 GET /api/v1/web/config
[2025-08-20 20:09:14.335770] [INFO process-70468-67360 bisheng.database.base:25] - trace=98136c54853a47bc8556c6675cea9b47 Session rollback because of exception:(pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '172.20.20.220' (timed out)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-08-20 20:09:14.381760] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c2a17ed5dd204f379c512cdf1bc982da GET /api/v1/user/info
[2025-08-20 20:09:14.399650] [ERROR process-70468-78376 __main__:33] - trace=1 GET http://127.0.0.1:7860/api/v1/web/config (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '172.20.20.220' (timed out)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-08-20 20:09:14.431420] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=668e0a36b75d4f49a579e6f1cd01e4c9 GET /api/v1/env
[2025-08-20 20:09:14.459414] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=89bbc02b4a6e4567a962633c156cf79f GET /api/v1/all
[2025-08-20 20:09:35.887883] [INFO process-70468-78060 bisheng.interface.custom.utils:343] - trace=89bbc02b4a6e4567a962633c156cf79f Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-20 20:09:35.916353] [ERROR process-70468-64000 __main__:33] - trace=1 GET http://127.0.0.1:7860/api/v1/user/info Timeout connecting to server
[2025-08-20 20:09:35.921327] [ERROR process-70468-69984 __main__:33] - trace=1 GET http://127.0.0.1:7860/api/v1/env Timeout connecting to server
[2025-08-20 20:09:36.410852] [INFO process-70468-78060 bisheng.interface.custom.utils:354] - trace=89bbc02b4a6e4567a962633c156cf79f Loading 1 component(s) from category custom_components
[2025-08-20 20:09:36.444289] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=035f63f3920542d99877f166b61c0da1 GET /api/v1/user/get_captcha
[2025-08-20 20:09:36.562264] [INFO process-70468-67360 bisheng.api.v1.user:673] - trace=035f63f3920542d99877f166b61c0da1 get_captcha captcha_char=kMcx
[2025-08-20 20:09:59.042440] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=89bbc02b4a6e4567a962633c156cf79f GET /api/v1/all 200 timecost=44583.026
[2025-08-20 20:09:59.061177] [ERROR process-70468-78060 __main__:33] - trace=1 GET http://127.0.0.1:7860/api/v1/user/get_captcha Timeout connecting to server
[2025-08-20 20:11:23.525083] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bf7788f2e29f485da0e97719fa099b06 GET /api/v1/user/public_key
[2025-08-20 20:11:24.388277] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bf7788f2e29f485da0e97719fa099b06 GET /api/v1/user/public_key 200 timecost=863.194
[2025-08-20 20:11:24.403773] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=591a3a8a29ae4cdebb9e4e5cae1644d6 POST /api/v1/user/login
[2025-08-20 20:11:26.496039] [INFO process-70468-67360 bisheng.api.services.audit_log:399] - trace=591a3a8a29ae4cdebb9e4e5cae1644d6 act=user_login user=Mao ip=127.0.0.1 user_id=12
[2025-08-20 20:11:27.604677] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=591a3a8a29ae4cdebb9e4e5cae1644d6 POST /api/v1/user/login 200 timecost=3200.904
[2025-08-20 20:11:27.612570] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e2904d38376a4ec187ab95c24c93338c GET /api/v1/user/get_captcha
[2025-08-20 20:11:27.632993] [INFO process-70468-67360 bisheng.api.v1.user:673] - trace=e2904d38376a4ec187ab95c24c93338c get_captcha captcha_char=8Fuz
[2025-08-20 20:11:27.835633] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e2904d38376a4ec187ab95c24c93338c GET /api/v1/user/get_captcha 200 timecost=223.064
[2025-08-20 20:11:28.785454] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=897f63f97f274b93abd0d43c94fe710b GET /api/v1/web/config
[2025-08-20 20:11:29.413175] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=897f63f97f274b93abd0d43c94fe710b GET /api/v1/web/config 200 timecost=627.721
[2025-08-20 20:11:38.118370] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0ada84ed76ed4ae394a0f9231c8b8e7e GET /api/v1/user/info
[2025-08-20 20:11:38.126635] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1a9de6d1331241af8e119ba610ab3e57 GET /api/v1/env
[2025-08-20 20:11:38.133325] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=301c40d255c046aa83f9fe0a1ce65682 GET /api/v1/all
[2025-08-20 20:11:38.155100] [INFO process-70468-1484 bisheng.interface.custom.utils:343] - trace=301c40d255c046aa83f9fe0a1ce65682 Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-20 20:11:38.601234] [INFO process-70468-1484 bisheng.interface.custom.utils:354] - trace=301c40d255c046aa83f9fe0a1ce65682 Loading 1 component(s) from category custom_components
[2025-08-20 20:11:41.871988] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0ada84ed76ed4ae394a0f9231c8b8e7e GET /api/v1/user/info 200 timecost=3753.618
[2025-08-20 20:11:41.888372] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=301c40d255c046aa83f9fe0a1ce65682 GET /api/v1/all 200 timecost=3754.365
[2025-08-20 20:11:41.904010] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1a9de6d1331241af8e119ba610ab3e57 GET /api/v1/env 200 timecost=3777.375
[2025-08-20 20:11:41.924716] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f324b4c4d048452da75aacb718668485 GET /api/v1/component
[2025-08-20 20:11:42.320293] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c787231edfe74100bbbe6c0cd05c48b6 GET /api/v1/llm/knowledge
[2025-08-20 20:11:42.335167] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=fa3135812d9444b08d88029c3f22b211 GET /api/v1/llm
[2025-08-20 20:11:42.350705] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c4f87cde477a43998ce3e61140880076 GET /api/v1/workstation/config
[2025-08-20 20:11:42.968890] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f324b4c4d048452da75aacb718668485 GET /api/v1/component 200 timecost=1044.175
[2025-08-20 20:11:42.983681] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f4a0894f788c4633a71d4b42a1f12dd3 GET /api/v1/knowledge
[2025-08-20 20:11:43.220070] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c787231edfe74100bbbe6c0cd05c48b6 GET /api/v1/llm/knowledge 200 timecost=899.776
[2025-08-20 20:11:43.312859] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=fa3135812d9444b08d88029c3f22b211 GET /api/v1/llm 200 timecost=978.247
[2025-08-20 20:11:46.260394] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f4a0894f788c4633a71d4b42a1f12dd3 GET /api/v1/knowledge 200 timecost=3276.714
[2025-08-20 20:11:46.269963] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c4f87cde477a43998ce3e61140880076 GET /api/v1/workstation/config 200 timecost=3919.819
[2025-08-20 20:11:52.215289] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d5e87579bf9a40ab81899ed54dd5f040 GET /api/v1/knowledge
[2025-08-20 20:11:52.701841] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d5e87579bf9a40ab81899ed54dd5f040 GET /api/v1/knowledge 200 timecost=486.552
[2025-08-20 20:11:58.330817] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=591f31bd52f242d18ead4842121cac0e GET /api/v1/knowledge
[2025-08-20 20:11:59.324797] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=591f31bd52f242d18ead4842121cac0e GET /api/v1/knowledge 200 timecost=993.981
[2025-08-20 20:12:05.273388] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=70d46c3848dd47b4bf759a3027aa2085 GET /api/v1/knowledge
[2025-08-20 20:12:06.287137] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=70d46c3848dd47b4bf759a3027aa2085 GET /api/v1/knowledge 200 timecost=1013.748
[2025-08-20 20:12:12.882006] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bc1d8eee3e064bfd97f864e6fed4b8ca GET /api/v1/knowledge
[2025-08-20 20:12:14.022956] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bc1d8eee3e064bfd97f864e6fed4b8ca GET /api/v1/knowledge 200 timecost=1140.95
[2025-08-20 20:12:19.968112] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=042ae56ba16143e2b860abcf9ae97569 GET /api/v1/knowledge
[2025-08-20 20:12:21.294937] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=042ae56ba16143e2b860abcf9ae97569 GET /api/v1/knowledge 200 timecost=1326.825
[2025-08-20 20:12:26.910469] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9832b49167dd45f69105f1cd120593f6 GET /api/v1/knowledge
[2025-08-20 20:12:27.902269] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9832b49167dd45f69105f1cd120593f6 GET /api/v1/knowledge 200 timecost=991.8
[2025-08-20 20:12:33.827714] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4a97c665a71a4b0282269efde769242c GET /api/v1/knowledge
[2025-08-20 20:12:35.138396] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4a97c665a71a4b0282269efde769242c GET /api/v1/knowledge 200 timecost=1310.682
[2025-08-20 20:12:40.760179] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3e50f460d41f4980ad69dc8bee723f64 GET /api/v1/knowledge
[2025-08-20 20:12:41.817695] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3e50f460d41f4980ad69dc8bee723f64 GET /api/v1/knowledge 200 timecost=1057.517
[2025-08-20 20:12:47.749218] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=650c4bbd5c44443794448f57cfde1957 GET /api/v1/knowledge
[2025-08-20 20:12:48.253009] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=650c4bbd5c44443794448f57cfde1957 GET /api/v1/knowledge 200 timecost=503.791
[2025-08-20 20:12:53.872691] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3b13a96b1ca642d8a28ef37d864818b3 GET /api/v1/knowledge
[2025-08-20 20:12:55.373200] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3b13a96b1ca642d8a28ef37d864818b3 GET /api/v1/knowledge 200 timecost=1500.51
[2025-08-20 20:13:01.302063] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0c9aae9bcf104017a2b4e19735c6daab GET /api/v1/knowledge
[2025-08-20 20:13:02.050524] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0c9aae9bcf104017a2b4e19735c6daab GET /api/v1/knowledge 200 timecost=748.461
[2025-08-20 20:13:07.672766] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6c7dd9a7b4664702991cf1068cfbf02a GET /api/v1/knowledge
[2025-08-20 20:13:08.610568] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6c7dd9a7b4664702991cf1068cfbf02a GET /api/v1/knowledge 200 timecost=937.803
[2025-08-20 20:13:14.543716] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b7d7469a933e4c12b4296c8058374eaf GET /api/v1/knowledge
[2025-08-20 20:13:15.787718] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b7d7469a933e4c12b4296c8058374eaf GET /api/v1/knowledge 200 timecost=1244.003
[2025-08-20 20:13:21.406007] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2fb49de2fa044f1785470c6d95873994 GET /api/v1/knowledge
[2025-08-20 20:13:23.453890] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2fb49de2fa044f1785470c6d95873994 GET /api/v1/knowledge 200 timecost=2047.883
[2025-08-20 20:13:29.383884] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a44fdd0d10854c39a407f761413b8216 GET /api/v1/knowledge
[2025-08-20 20:13:30.959741] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a44fdd0d10854c39a407f761413b8216 GET /api/v1/knowledge 200 timecost=1575.857
[2025-08-20 20:13:36.574782] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=eabde633d3d84352b69c0a073b24594f GET /api/v1/knowledge
[2025-08-20 20:13:37.316923] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=eabde633d3d84352b69c0a073b24594f GET /api/v1/knowledge 200 timecost=742.142
[2025-08-20 20:13:43.241819] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=679e5ebfb440463d9f694e7f89d796c6 GET /api/v1/knowledge
[2025-08-20 20:13:44.228908] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=679e5ebfb440463d9f694e7f89d796c6 GET /api/v1/knowledge 200 timecost=987.089
[2025-08-20 20:13:49.844404] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=78bb9d892dd94fe28d2efaf70d7f5e73 GET /api/v1/knowledge
[2025-08-20 20:13:50.604364] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=78bb9d892dd94fe28d2efaf70d7f5e73 GET /api/v1/knowledge 200 timecost=759.96
[2025-08-20 20:13:56.523241] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8185cb40b5c44b00bd6e6802d217d818 GET /api/v1/knowledge
[2025-08-20 20:13:58.042614] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8185cb40b5c44b00bd6e6802d217d818 GET /api/v1/knowledge 200 timecost=1519.373
[2025-08-20 20:14:03.664396] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3c13922d7c8d4f288b6c1301ca798519 GET /api/v1/knowledge
[2025-08-20 20:14:04.938656] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3c13922d7c8d4f288b6c1301ca798519 GET /api/v1/knowledge 200 timecost=1275.278
[2025-08-20 20:14:10.861493] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6d306e7698ee4136a12d759754c4ad5d GET /api/v1/knowledge
[2025-08-20 20:14:11.624261] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6d306e7698ee4136a12d759754c4ad5d GET /api/v1/knowledge 200 timecost=761.758
[2025-08-20 20:14:17.244884] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b9a63e887a4843949aeef17bac59336c GET /api/v1/knowledge
[2025-08-20 20:14:17.998251] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b9a63e887a4843949aeef17bac59336c GET /api/v1/knowledge 200 timecost=753.366
[2025-08-20 20:14:23.926105] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=eb209bef7b44490599d319e568b79449 GET /api/v1/knowledge
[2025-08-20 20:14:24.942357] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=eb209bef7b44490599d319e568b79449 GET /api/v1/knowledge 200 timecost=1017.264
[2025-08-20 20:14:30.565722] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=641ad830a3114c278a15e39e25979872 GET /api/v1/knowledge
[2025-08-20 20:14:31.563941] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=641ad830a3114c278a15e39e25979872 GET /api/v1/knowledge 200 timecost=998.219
[2025-08-20 20:14:37.489765] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=23c3772b633a47bd8e7a1c3db83415cb GET /api/v1/knowledge
[2025-08-20 20:14:38.241543] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=23c3772b633a47bd8e7a1c3db83415cb GET /api/v1/knowledge 200 timecost=751.778
[2025-08-20 20:14:43.859940] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d2fb004d6eea4702a8dcb211429484f1 GET /api/v1/knowledge
[2025-08-20 20:14:44.616852] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d2fb004d6eea4702a8dcb211429484f1 GET /api/v1/knowledge 200 timecost=755.912
[2025-08-20 20:14:50.538024] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=5cc4f2a2b19845aea63a7ab7bfde51be GET /api/v1/knowledge
[2025-08-20 20:14:51.814706] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=5cc4f2a2b19845aea63a7ab7bfde51be GET /api/v1/knowledge 200 timecost=1276.682
[2025-08-20 20:14:57.437247] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3e60d8311d5e4c1e95c570372a000965 GET /api/v1/knowledge
[2025-08-20 20:14:58.756560] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3e60d8311d5e4c1e95c570372a000965 GET /api/v1/knowledge 200 timecost=1319.314
[2025-08-20 20:15:04.686777] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=91953be455904e16be95ec52ab5fe141 GET /api/v1/knowledge
[2025-08-20 20:15:05.448495] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=91953be455904e16be95ec52ab5fe141 GET /api/v1/knowledge 200 timecost=761.718
[2025-08-20 20:15:11.530363] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a8a2c7040cc64c6b9969da771dba1e74 GET /api/v1/knowledge
[2025-08-20 20:15:12.576621] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a8a2c7040cc64c6b9969da771dba1e74 GET /api/v1/knowledge 200 timecost=1045.248
[2025-08-20 20:15:18.558986] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1b46cab7b055447fb794bca5d55d0253 GET /api/v1/knowledge
[2025-08-20 20:15:19.350474] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1b46cab7b055447fb794bca5d55d0253 GET /api/v1/knowledge 200 timecost=792.92
[2025-08-20 20:15:24.971172] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a7c3d15378e8402095e3cd24ec0d9d0a GET /api/v1/knowledge
[2025-08-20 20:15:25.683308] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a7c3d15378e8402095e3cd24ec0d9d0a GET /api/v1/knowledge 200 timecost=712.136
[2025-08-20 20:15:31.599262] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9fc815f8b8a947128cc98353ee0cef60 GET /api/v1/knowledge
[2025-08-20 20:15:32.620632] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9fc815f8b8a947128cc98353ee0cef60 GET /api/v1/knowledge 200 timecost=1021.37
[2025-08-20 20:15:38.464316] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e8367d04b13d492fa53187273959e3b1 GET /api/v1/knowledge
[2025-08-20 20:15:39.044005] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e8367d04b13d492fa53187273959e3b1 GET /api/v1/knowledge 200 timecost=579.688
[2025-08-20 20:15:42.685614] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=67d19bcbc8674c529915d8244cca95f4 GET /api/v1/skill/template
[2025-08-20 20:15:43.001330] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e0760c811900469b905785b207e61567 GET /api/v1/tag
[2025-08-20 20:15:43.202751] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=67d19bcbc8674c529915d8244cca95f4 GET /api/v1/skill/template 200 timecost=517.138
[2025-08-20 20:15:43.582945] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c23b97b829ec4e05a8ef4f15ce4f0c5b GET /api/v1/workflow/list
[2025-08-20 20:15:43.982327] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e0760c811900469b905785b207e61567 GET /api/v1/tag 200 timecost=980.997
[2025-08-20 20:15:44.995853] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d9396d815bb941e381154bc5eb2018bc GET /api/v1/knowledge
[2025-08-20 20:15:45.184104] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c23b97b829ec4e05a8ef4f15ce4f0c5b GET /api/v1/workflow/list 200 timecost=1601.158
[2025-08-20 20:15:46.295495] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d9396d815bb941e381154bc5eb2018bc GET /api/v1/knowledge 200 timecost=1299.641
[2025-08-20 20:15:46.301781] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1bf27458cd654daf94b036d0e0c510bb GET /api/v1/skill/template
[2025-08-20 20:15:47.339083] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1bf27458cd654daf94b036d0e0c510bb GET /api/v1/skill/template 200 timecost=1038.02
[2025-08-20 20:15:49.237307] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=63401a039cdd41f992940029e8451df9 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627
[2025-08-20 20:15:49.709313] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=63401a039cdd41f992940029e8451df9 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627 200 timecost=472.007
[2025-08-20 20:15:50.452956] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=19dae962b82e4fd2be5e1b0615790b4c GET /api/v1/workflow/versions
[2025-08-20 20:15:50.466606] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=932ad6f6b4b140f08bdd7f7d1fd42ef7 GET /api/v1/assistant/tool_list
[2025-08-20 20:15:50.476573] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=27ace6ef20b64543b7641196586cf2b7 GET /api/v1/knowledge
[2025-08-20 20:15:50.485150] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=af13654cbd37439c8f205753bd684cdd GET /api/v1/llm
[2025-08-20 20:15:50.499639] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d4dc91ee5e904115b9e72870b3d0b09e GET /api/v1/llm/workflow
[2025-08-20 20:15:51.171129] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=19dae962b82e4fd2be5e1b0615790b4c GET /api/v1/workflow/versions 200 timecost=718.172
[2025-08-20 20:15:51.314078] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d4dc91ee5e904115b9e72870b3d0b09e GET /api/v1/llm/workflow 200 timecost=815.436
[2025-08-20 20:15:51.567583] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=27ace6ef20b64543b7641196586cf2b7 GET /api/v1/knowledge 200 timecost=1091.011
[2025-08-20 20:15:51.935495] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=af13654cbd37439c8f205753bd684cdd GET /api/v1/llm 200 timecost=1451.343
[2025-08-20 20:15:52.084102] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=932ad6f6b4b140f08bdd7f7d1fd42ef7 GET /api/v1/assistant/tool_list 200 timecost=1617.496
[2025-08-20 20:15:52.480078] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c0476e48789d47388b28a50f0f1b09fc GET /api/v1/workflow/versions
[2025-08-20 20:15:52.741050] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c0476e48789d47388b28a50f0f1b09fc GET /api/v1/workflow/versions 200 timecost=260.972
[2025-08-20 20:15:52.790989] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d8badfc75fa04fd98addbf4421c5505a GET /api/v1/assistant/tool_list
[2025-08-20 20:15:52.801133] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3a4a9cc77ebd44bf905ebd0523a2a834 GET /api/v1/knowledge
[2025-08-20 20:15:52.808824] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0c24a12c776e4782b5731f720bed21ea GET /api/v1/llm
[2025-08-20 20:15:52.816385] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e9433eadc4de450a970d440189243fde GET /api/v1/llm/workflow
[2025-08-20 20:15:53.590484] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e9433eadc4de450a970d440189243fde GET /api/v1/llm/workflow 200 timecost=774.099
[2025-08-20 20:15:53.726630] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0c24a12c776e4782b5731f720bed21ea GET /api/v1/llm 200 timecost=917.806
[2025-08-20 20:15:53.733803] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=acb3376f774944a3976bb3a8a2a7b667 GET /api/v1/knowledge/info
[2025-08-20 20:15:54.450566] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3a4a9cc77ebd44bf905ebd0523a2a834 GET /api/v1/knowledge 200 timecost=1649.433
[2025-08-20 20:15:54.459319] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=acb3376f774944a3976bb3a8a2a7b667 GET /api/v1/knowledge/info 200 timecost=725.517
[2025-08-20 20:15:54.823614] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=97b4798aa7e1441b9309c86d1bc6e113 trace_id=5267cfbdfcef442b86b17519fedb70aa message={'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id': 12, 'flow_type': 10, 'update_time': '2025-08-07T15:12:45', 'id': '0c26e44b161448ca913617e3c857d627', 'status': 2, 'description': '', 'guide_word': None, 'create_time': '2025-07-31T15:46:27', 'nodes': [{'id': 'start_25626', 'data': {'v': '1', 'id': 'start_25626', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': [''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': '1afc3c', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 615}, 'position': {'x': 720, 'y': 20}, 'selected': False}, {'id': 'input_1fb3a', 'data': {'v': '2', 'id': 'input_1fb3a', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '输入文本内容', 'global': 'key'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 422}, 'position': {'x': 1155, 'y': 15}, 'selected': False}, {'id': 'rag_63d89', 'data': {'v': '1', 'id': 'rag_63d89', 'name': '文档知识库问答', 'type': 'rag', 'description': '根据用户问题从知识库中检索相关内容，结合检索结果调用大模型生成最终结果，支持多个问题并行执行。', 'group_params': [{'name': '知识库检索设置', 'params': [{'key': 'user_question', 'help': '当选择多个问题时，将会多次运行本节点，每次运行时从批量问题中取一项进行处理。', 'test': 'var', 'type': 'user_question', 'label': '用户问题', 'value': ['input_1fb3a.user_input'], 'varZh': {'input_1fb3a.user_input': '输入/user_input'}, 'global': 'self=user_prompt', 'linkage': 'output_user_input', 'required': True, 'placeholder': '请选择用户问题'}, {'key': 'knowledge', 'type': 'knowledge_select_multi', 'label': '检索范围', 'value': {'type': 'knowledge', 'value': [{'key': 124, 'label': '比亚迪-秦PLUS-车型知识库'}]}, 'required': True, 'placeholder': '请选择知识库'}, {'key': 'user_auth', 'help': '开启后，只会对用户有使用权限的知识库进行检索。', 'type': 'switch', 'label': '用户知识库权限校验', 'value': False}, {'key': 'max_chunk_size', 'help': '通过此参数控制最终传给模型的知识库检索结果文本长度，超过模型支持的最大上下文长度可能会导致报错。', 'type': 'number', 'label': '检索结果长度', 'value': 15000}, {'key': 'retrieved_result', 'type': 'var', 'label': '检索结果', 'global': 'self=user_prompt'}]}, {'name': 'AI回复生成设置', 'params': [{'key': 'system_prompt', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'required': True}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '用户问题：```{{#rag_63d89.user_question#}}```\n参考文本：```{{#rag_63d89.retrieved_result#}}```\n你的回答：', 'varZh': {'rag_63d89.user_question': 'user_question', 'rag_63d89.retrieved_result': 'retrieved_result'}, 'required': True}, {'key': 'model_id', 'type': 'bisheng_model', 'label': '模型', 'value': 82, 'required': True, 'placeholder': '请在模型管理中配置 LLM 模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output_user_input', 'help': '模型输出内容将会存储在该变量中。', 'type': 'var', 'label': '输出变量', 'value': [{'key': 'output_user_input', 'label': 'output_user_input'}], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1099}, 'position': {'x': 1575, 'y': 15}, 'selected': True}], 'edges': [{'id': 'xy-edge__input_1fb3aright_handle-rag_63d89left_handle', 'type': 'customEdge', 'source': 'input_1fb3a', 'target': 'rag_63d89', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__start_25626right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'start_25626', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__rag_63d89right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'rag_63d89', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': -31.242038216560445, 'y': 26.68152866242042, 'zoom': 0.6878980891719745}}}
[2025-08-20 20:15:54.913725] [INFO process-70468-72480 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 20:15:54.970708] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d8badfc75fa04fd98addbf4421c5505a GET /api/v1/assistant/tool_list 200 timecost=2179.719
[2025-08-20 20:15:54.992700] [INFO process-70468-71428 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 20:15:55.989251] [INFO process-70468-72480 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-20 20:15:56.005489] [INFO process-70468-71428 bisheng.utils.threadpool:67] - trace=5267cfbdfcef442b86b17519fedb70aa async_task_added fun=wrapper_task args=5267cfbdfcef442b86b17519fedb70aa
[2025-08-20 20:16:19.408532] [INFO process-70468-67360 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1005, None)
[2025-08-20 20:16:19.416944] [INFO process-70468-67360 bisheng.chat.manager:181] - trace=1 close_client client_key=97b4798aa7e1441b9309c86d1bc6e113
[2025-08-20 20:16:19.886426] [ERROR process-70468-73808 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 12, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "97b4798aa7e1441b9309c86d1bc6e113"}', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'chat_id': ''} error: Unexpected ASGI message 'websocket.send', after sending 'websocket.close'.
[2025-08-20 20:16:19.974641] [ERROR process-70468-67360 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-20 20:16:19.978646] [WARNING process-70468-67360 bisheng.chat.manager:179] - trace=1 close_client client_key=97b4798aa7e1441b9309c86d1bc6e113 not in active_clients
[2025-08-20 20:16:19.983624] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8b7b11297fa847d0b48710ba30ec5ef3 GET /api/v1/skill/template
[2025-08-20 20:16:20.087876] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=63e7f587f627464889ff6bd404a57eb6 GET /api/v1/workflow/list
[2025-08-20 20:16:21.041905] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8b7b11297fa847d0b48710ba30ec5ef3 GET /api/v1/skill/template 200 timecost=1058.281
[2025-08-20 20:16:21.325049] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=63e7f587f627464889ff6bd404a57eb6 GET /api/v1/workflow/list 200 timecost=1237.174
[2025-08-20 20:16:21.359517] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=55f6f571d2d047e7849ba872548290ad GET /api/v1/skill/template
[2025-08-20 20:16:21.593589] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=55f6f571d2d047e7849ba872548290ad GET /api/v1/skill/template 200 timecost=234.071
[2025-08-20 20:17:04.313932] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c8f1c56e9a2f4208a735cf8d29dbf796 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627
[2025-08-20 20:17:05.014431] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c8f1c56e9a2f4208a735cf8d29dbf796 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627 200 timecost=700.499
[2025-08-20 20:17:05.365401] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f931e88c5e2d49ada1102245589dce01 GET /api/v1/workflow/versions
[2025-08-20 20:17:05.678092] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=dca94cb16a6e447f81c8cab6643e56e9 GET /api/v1/assistant/tool_list
[2025-08-20 20:17:05.692348] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c1ae3783d3bc4f2ba52eb2d6eb72cb1a GET /api/v1/knowledge
[2025-08-20 20:17:05.700409] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=27992ef7f16248d587939e1081a09ed6 GET /api/v1/llm
[2025-08-20 20:17:05.717419] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4c29de3158fc43a2aade5fe7e2914257 GET /api/v1/llm/workflow
[2025-08-20 20:17:06.774377] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f931e88c5e2d49ada1102245589dce01 GET /api/v1/workflow/versions 200 timecost=1408.975
[2025-08-20 20:17:07.704667] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c1ae3783d3bc4f2ba52eb2d6eb72cb1a GET /api/v1/knowledge 200 timecost=2012.319
[2025-08-20 20:17:07.711681] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=27992ef7f16248d587939e1081a09ed6 GET /api/v1/llm 200 timecost=2012.301
[2025-08-20 20:17:07.719780] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4c29de3158fc43a2aade5fe7e2914257 GET /api/v1/llm/workflow 200 timecost=2002.361
[2025-08-20 20:17:07.726790] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ffe4c05bc3ac40549d7dfd722cbe51ce GET /api/v1/knowledge/info
[2025-08-20 20:17:08.103843] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=dca94cb16a6e447f81c8cab6643e56e9 GET /api/v1/assistant/tool_list 200 timecost=2425.75
[2025-08-20 20:17:08.112266] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ffe4c05bc3ac40549d7dfd722cbe51ce GET /api/v1/knowledge/info 200 timecost=385.476
[2025-08-20 20:17:08.481215] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=dfab6c3d9fee4095abad176324ddf9e7 trace_id=ea62136b335444d19240495ffd1e4800 message={'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id': 12, 'flow_type': 10, 'update_time': '2025-08-07T15:12:45', 'id': '0c26e44b161448ca913617e3c857d627', 'status': 2, 'description': '', 'guide_word': None, 'create_time': '2025-07-31T15:46:27', 'nodes': [{'id': 'start_25626', 'data': {'v': '1', 'id': 'start_25626', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': [''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': '1afc3c', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 615}, 'position': {'x': 720, 'y': 20}, 'selected': False}, {'id': 'input_1fb3a', 'data': {'v': '2', 'id': 'input_1fb3a', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '输入文本内容', 'global': 'key'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 422}, 'position': {'x': 1155, 'y': 15}, 'selected': False}, {'id': 'rag_63d89', 'data': {'v': '1', 'id': 'rag_63d89', 'name': '文档知识库问答', 'type': 'rag', 'description': '根据用户问题从知识库中检索相关内容，结合检索结果调用大模型生成最终结果，支持多个问题并行执行。', 'group_params': [{'name': '知识库检索设置', 'params': [{'key': 'user_question', 'help': '当选择多个问题时，将会多次运行本节点，每次运行时从批量问题中取一项进行处理。', 'test': 'var', 'type': 'user_question', 'label': '用户问题', 'value': ['input_1fb3a.user_input'], 'varZh': {'input_1fb3a.user_input': '输入/user_input'}, 'global': 'self=user_prompt', 'linkage': 'output_user_input', 'required': True, 'placeholder': '请选择用户问题'}, {'key': 'knowledge', 'type': 'knowledge_select_multi', 'label': '检索范围', 'value': {'type': 'knowledge', 'value': [{'key': 124, 'label': '比亚迪-秦PLUS-车型知识库'}]}, 'required': True, 'placeholder': '请选择知识库'}, {'key': 'user_auth', 'help': '开启后，只会对用户有使用权限的知识库进行检索。', 'type': 'switch', 'label': '用户知识库权限校验', 'value': False}, {'key': 'max_chunk_size', 'help': '通过此参数控制最终传给模型的知识库检索结果文本长度，超过模型支持的最大上下文长度可能会导致报错。', 'type': 'number', 'label': '检索结果长度', 'value': 15000}, {'key': 'retrieved_result', 'type': 'var', 'label': '检索结果', 'global': 'self=user_prompt'}]}, {'name': 'AI回复生成设置', 'params': [{'key': 'system_prompt', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'required': True}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '用户问题：```{{#rag_63d89.user_question#}}```\n参考文本：```{{#rag_63d89.retrieved_result#}}```\n你的回答：', 'varZh': {'rag_63d89.user_question': 'user_question', 'rag_63d89.retrieved_result': 'retrieved_result'}, 'required': True}, {'key': 'model_id', 'type': 'bisheng_model', 'label': '模型', 'value': 82, 'required': True, 'placeholder': '请在模型管理中配置 LLM 模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output_user_input', 'help': '模型输出内容将会存储在该变量中。', 'type': 'var', 'label': '输出变量', 'value': [{'key': 'output_user_input', 'label': 'output_user_input'}], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1099}, 'position': {'x': 1575, 'y': 15}, 'selected': True}], 'edges': [{'id': 'xy-edge__input_1fb3aright_handle-rag_63d89left_handle', 'type': 'customEdge', 'source': 'input_1fb3a', 'target': 'rag_63d89', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__start_25626right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'start_25626', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__rag_63d89right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'rag_63d89', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': -31.242038216560445, 'y': 26.68152866242042, 'zoom': 0.6878980891719745}}}
[2025-08-20 20:17:08.538632] [INFO process-70468-49076 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 20:17:08.566660] [INFO process-70468-72480 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 20:17:09.567040] [INFO process-70468-49076 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-20 20:17:09.582558] [INFO process-70468-72480 bisheng.utils.threadpool:67] - trace=ea62136b335444d19240495ffd1e4800 async_task_added fun=wrapper_task args=ea62136b335444d19240495ffd1e4800
[2025-08-20 20:18:56.179986] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=dfab6c3d9fee4095abad176324ddf9e7 trace_id=f48ab274f1b34a62b95298499ba43260 message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': '你好，请问你知道什么汽车？', 'dialog_files_content': []}, 'message': '你好，请问你知道什么汽车？', 'message_id': 'bae125a8b2b447228f0e56c8e20fa939', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 20:18:56.206787] [INFO process-70468-71428 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 20:18:56.233709] [INFO process-70468-2632 bisheng.chat.clients.workflow_client:182] - trace=f48ab274f1b34a62b95298499ba43260 get user input: {'input_1fb3a': {'data': {'user_input': '你好，请问你知道什么汽车？', 'dialog_files_content': []}, 'message': '你好，请问你知道什么汽车？', 'message_id': 'bae125a8b2b447228f0e56c8e20fa939', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 20:18:57.234766] [INFO process-70468-71428 bisheng.utils.threadpool:67] - trace=f48ab274f1b34a62b95298499ba43260 async_task_added fun=wrapper_task args=f48ab274f1b34a62b95298499ba43260
[2025-08-20 20:19:55.630014] [INFO process-70468-67360 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1005, None)
[2025-08-20 20:19:55.638573] [INFO process-70468-67360 bisheng.chat.manager:181] - trace=1 close_client client_key=dfab6c3d9fee4095abad176324ddf9e7
[2025-08-20 20:19:55.646916] [ERROR process-70468-67360 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-20 20:19:55.653747] [ERROR process-70468-77592 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 12, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "dfab6c3d9fee4095abad176324ddf9e7"}', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'chat_id': ''} error: Cannot call "send" once a close message has been sent.
[2025-08-20 20:19:55.665457] [WARNING process-70468-67360 bisheng.chat.manager:179] - trace=1 close_client client_key=dfab6c3d9fee4095abad176324ddf9e7 not in active_clients
[2025-08-20 20:19:55.707073] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f608427d8818458ba1bd77586e2ed497 GET /api/v1/skill/template
[2025-08-20 20:19:56.250768] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2dc6c066d5e1427d937ad9a3962adbee GET /api/v1/workflow/list
[2025-08-20 20:19:56.391821] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f608427d8818458ba1bd77586e2ed497 GET /api/v1/skill/template 200 timecost=685.259
[2025-08-20 20:19:58.181485] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=cc71c1dc6ad94608814a1610ac2080d4 GET /api/v1/group/list
[2025-08-20 20:19:58.191122] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ce8df0e07fe64b999c88f7d1333a332b GET /api/v1/role/list
[2025-08-20 20:19:59.204971] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=cc71c1dc6ad94608814a1610ac2080d4 GET /api/v1/group/list 200 timecost=1023.487
[2025-08-20 20:19:59.215214] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ce8df0e07fe64b999c88f7d1333a332b GET /api/v1/role/list 200 timecost=1024.091
[2025-08-20 20:19:59.224446] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2dc6c066d5e1427d937ad9a3962adbee GET /api/v1/workflow/list 200 timecost=2974.98
[2025-08-20 20:19:59.236244] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=51e96f26dbd54a5b8d818419e5f1eb71 GET /api/v1/user/list
[2025-08-20 20:20:10.233199] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=51e96f26dbd54a5b8d818419e5f1eb71 GET /api/v1/user/list 201 timecost=10996.954
[2025-08-20 20:20:10.242828] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d868a4ef239c48028e255332ca38e698 GET /api/v1/config
[2025-08-20 20:20:10.251328] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=09a297924c754e6da0c9d6f546a9e0d7 POST /api/v1/user/logout
[2025-08-20 20:20:10.416439] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=09a297924c754e6da0c9d6f546a9e0d7 POST /api/v1/user/logout 201 timecost=165.111
[2025-08-20 20:20:10.756396] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ea2ea9517a784de493a6c6f6db5ba8ad GET /api/v1/user/get_captcha
[2025-08-20 20:20:10.783302] [INFO process-70468-67360 bisheng.api.v1.user:673] - trace=ea2ea9517a784de493a6c6f6db5ba8ad get_captcha captcha_char=otiS
[2025-08-20 20:20:10.936217] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ea2ea9517a784de493a6c6f6db5ba8ad GET /api/v1/user/get_captcha 200 timecost=179.821
[2025-08-20 20:20:10.942648] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d868a4ef239c48028e255332ca38e698 GET /api/v1/config 200 timecost=699.821
[2025-08-20 20:20:24.234574] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f1972b0b78c841fbb7e3e4390d86d755 GET /api/v1/user/public_key
[2025-08-20 20:20:24.588811] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f1972b0b78c841fbb7e3e4390d86d755 GET /api/v1/user/public_key 200 timecost=354.237
[2025-08-20 20:20:24.937245] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6b938669d3f54796a9c8cd40ae9c0052 POST /api/v1/user/login
[2025-08-20 20:20:24.940999] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=409070d938a444c598d523ae78a23ed1 GET /api/v1/user/get_captcha
[2025-08-20 20:20:24.953624] [INFO process-70468-67360 bisheng.api.v1.user:673] - trace=409070d938a444c598d523ae78a23ed1 get_captcha captcha_char=1HWA
[2025-08-20 20:20:25.105488] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=409070d938a444c598d523ae78a23ed1 GET /api/v1/user/get_captcha 200 timecost=164.489
[2025-08-20 20:20:26.201267] [INFO process-70468-67360 bisheng.api.services.audit_log:399] - trace=6b938669d3f54796a9c8cd40ae9c0052 act=user_login user=Mao ip=127.0.0.1 user_id=12
[2025-08-20 20:20:26.539243] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6b938669d3f54796a9c8cd40ae9c0052 POST /api/v1/user/login 200 timecost=1601.998
[2025-08-20 20:20:26.801373] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c54bd4c1c437443eae2478cb222e9092 GET /api/v1/web/config
[2025-08-20 20:20:26.935489] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c54bd4c1c437443eae2478cb222e9092 GET /api/v1/web/config 200 timecost=134.117
[2025-08-20 20:20:29.886792] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=5ae9060c5ec44f5a9dc86729136dab28 GET /api/v1/user/info
[2025-08-20 20:20:29.895846] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=01ad8975a1ec46348887652b25f4d60f GET /api/v1/env
[2025-08-20 20:20:29.904895] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a48dd89c4c924d7999de03ed1cc659ef GET /api/v1/all
[2025-08-20 20:20:29.934518] [INFO process-70468-82292 bisheng.interface.custom.utils:343] - trace=a48dd89c4c924d7999de03ed1cc659ef Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-20 20:20:30.577515] [INFO process-70468-82292 bisheng.interface.custom.utils:354] - trace=a48dd89c4c924d7999de03ed1cc659ef Loading 1 component(s) from category custom_components
[2025-08-20 20:20:33.681502] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=5ae9060c5ec44f5a9dc86729136dab28 GET /api/v1/user/info 200 timecost=3793.696
[2025-08-20 20:20:33.693138] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a48dd89c4c924d7999de03ed1cc659ef GET /api/v1/all 200 timecost=3789.243
[2025-08-20 20:20:33.703738] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=01ad8975a1ec46348887652b25f4d60f GET /api/v1/env 200 timecost=3808.405
[2025-08-20 20:20:33.720850] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3eafaecb72c24b298e46d15708d2bcc3 GET /api/v1/component
[2025-08-20 20:20:33.939975] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3dc963f1eec64774a86de292f7038c89 GET /api/v1/group/list
[2025-08-20 20:20:33.950120] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7679f3b79e99495c8276841d684b935c GET /api/v1/role/list
[2025-08-20 20:20:35.864471] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3dc963f1eec64774a86de292f7038c89 GET /api/v1/group/list 200 timecost=1924.496
[2025-08-20 20:20:35.874006] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7679f3b79e99495c8276841d684b935c GET /api/v1/role/list 200 timecost=1923.886
[2025-08-20 20:20:35.880072] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3eafaecb72c24b298e46d15708d2bcc3 GET /api/v1/component 200 timecost=2159.221
[2025-08-20 20:20:35.888317] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=39ca91af64c04e7e8f4afa636cec218e GET /api/v1/workstation/config
[2025-08-20 20:20:35.900032] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d433500ff08e4bb78d053155ee4fdf84 GET /api/v1/user/list
[2025-08-20 20:20:48.750420] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d433500ff08e4bb78d053155ee4fdf84 GET /api/v1/user/list 201 timecost=12850.389
[2025-08-20 20:20:48.768663] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a54758b9125d4c74ad91c78eca6807c2 GET /api/v1/llm
[2025-08-20 20:20:49.245300] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a54758b9125d4c74ad91c78eca6807c2 GET /api/v1/llm 200 timecost=476.637
[2025-08-20 20:20:49.263396] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9eb379f641ca48cd9cab87ed85fb6c44 GET /api/v1/llm
[2025-08-20 20:20:49.768189] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9eb379f641ca48cd9cab87ed85fb6c44 GET /api/v1/llm 200 timecost=506.12
[2025-08-20 20:20:50.687137] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=39ca91af64c04e7e8f4afa636cec218e GET /api/v1/workstation/config 200 timecost=14798.82
[2025-08-20 20:20:51.320105] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e8dd38596b68451992ce5b13daaa04c0 GET /api/v1/llm/info
[2025-08-20 20:20:52.488234] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e8dd38596b68451992ce5b13daaa04c0 GET /api/v1/llm/info 200 timecost=1168.641
[2025-08-20 20:20:53.817265] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ef05c392da574bc3a7eafd427f33661f GET /api/v1/group/list
[2025-08-20 20:20:55.387624] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ef05c392da574bc3a7eafd427f33661f GET /api/v1/group/list 200 timecost=1569.362
[2025-08-20 20:20:55.398131] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bef9b11f692e41dbb43b17ad5a482ed6 GET /api/v1/role/list
[2025-08-20 20:20:55.406682] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=580c9325767445e59001019737b9a799 GET /api/v1/user/list
[2025-08-20 20:21:08.338266] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bef9b11f692e41dbb43b17ad5a482ed6 GET /api/v1/role/list 200 timecost=12940.135
[2025-08-20 20:21:08.346177] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=580c9325767445e59001019737b9a799 GET /api/v1/user/list 201 timecost=12939.496
[2025-08-20 20:21:37.409527] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c7d2ea47ccee43c996cdec8881e7698f GET /api/v1/config
[2025-08-20 20:21:38.196892] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c7d2ea47ccee43c996cdec8881e7698f GET /api/v1/config 200 timecost=787.875
[2025-08-20 20:21:38.943188] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1cf3201592f24e3995de2c07defbb021 GET /api/v1/llm
[2025-08-20 20:21:40.057701] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1cf3201592f24e3995de2c07defbb021 GET /api/v1/llm 200 timecost=1115.515
[2025-08-20 20:21:53.102284] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b5aac3d47ab8479da9b08b069d8d466f GET /api/v1/llm
[2025-08-20 20:21:54.055860] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b5aac3d47ab8479da9b08b069d8d466f GET /api/v1/llm 200 timecost=954.59
[2025-08-20 20:21:55.524942] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b978d5fae1ad4707b154fb640363ecdf GET /api/v1/skill/template
[2025-08-20 20:21:55.534082] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8e8cbd8aefe0404aae614628eed63d5c GET /api/v1/tag
[2025-08-20 20:21:55.892300] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4efb792fcfcb4081ba0078edd9dd5774 GET /api/v1/workflow/list
[2025-08-20 20:21:56.091346] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b978d5fae1ad4707b154fb640363ecdf GET /api/v1/skill/template 200 timecost=566.404
[2025-08-20 20:21:56.451484] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8e8cbd8aefe0404aae614628eed63d5c GET /api/v1/tag 200 timecost=917.401
[2025-08-20 20:21:57.250519] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4efb792fcfcb4081ba0078edd9dd5774 GET /api/v1/workflow/list 200 timecost=1358.22
[2025-08-20 20:21:57.622510] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6f2a247e7b274f1a82f9861d248a2deb GET /api/v1/skill/template
[2025-08-20 20:21:57.958941] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6f2a247e7b274f1a82f9861d248a2deb GET /api/v1/skill/template 200 timecost=336.432
[2025-08-20 20:21:59.857893] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6c191528127c4361985d691f2ddb0521 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627
[2025-08-20 20:22:00.164398] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6c191528127c4361985d691f2ddb0521 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627 200 timecost=306.505
[2025-08-20 20:22:00.628181] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c52f97c68ef942ce84e0471e66c08bec GET /api/v1/workflow/versions
[2025-08-20 20:22:00.947229] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a3360630a5194d039c8d6c8744cbad64 GET /api/v1/assistant/tool_list
[2025-08-20 20:22:00.958026] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=86f973c0360b482687e9a76f7b2f1831 GET /api/v1/knowledge
[2025-08-20 20:22:00.970524] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=c713746d356d4eaa89a64be8dc845360 GET /api/v1/llm
[2025-08-20 20:22:00.981342] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=be1677f0955a40ddad5ba5a5c3e9e9fd GET /api/v1/llm/workflow
[2025-08-20 20:22:01.746782] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c52f97c68ef942ce84e0471e66c08bec GET /api/v1/workflow/versions 200 timecost=1119.617
[2025-08-20 20:22:02.033136] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=c713746d356d4eaa89a64be8dc845360 GET /api/v1/llm 200 timecost=1063.615
[2025-08-20 20:22:02.383942] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=be1677f0955a40ddad5ba5a5c3e9e9fd GET /api/v1/llm/workflow 200 timecost=1402.6
[2025-08-20 20:22:02.737795] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=86f973c0360b482687e9a76f7b2f1831 GET /api/v1/knowledge 200 timecost=1779.769
[2025-08-20 20:22:02.750877] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a3360630a5194d039c8d6c8744cbad64 GET /api/v1/assistant/tool_list 200 timecost=1803.648
[2025-08-20 20:22:06.285973] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=cc17154879c44fff836c0d05b5819a6d GET /api/v1/knowledge/info
[2025-08-20 20:22:06.731181] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=cc17154879c44fff836c0d05b5819a6d GET /api/v1/knowledge/info 200 timecost=445.208
[2025-08-20 20:22:07.127254] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=4d2a186949014c1cbf70186befd24fc5 trace_id=e307349e6ffe4ba0a7a228f4ecd34f0d message={'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id': 12, 'flow_type': 10, 'update_time': '2025-08-07T15:12:45', 'id': '0c26e44b161448ca913617e3c857d627', 'status': 2, 'description': '', 'guide_word': None, 'create_time': '2025-07-31T15:46:27', 'nodes': [{'id': 'start_25626', 'data': {'v': '1', 'id': 'start_25626', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': [''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': '1afc3c', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 615}, 'position': {'x': 720, 'y': 20}, 'selected': False}, {'id': 'input_1fb3a', 'data': {'v': '2', 'id': 'input_1fb3a', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '输入文本内容', 'global': 'key'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 422}, 'position': {'x': 1155, 'y': 15}, 'selected': False}, {'id': 'rag_63d89', 'data': {'v': '1', 'id': 'rag_63d89', 'name': '文档知识库问答', 'type': 'rag', 'description': '根据用户问题从知识库中检索相关内容，结合检索结果调用大模型生成最终结果，支持多个问题并行执行。', 'group_params': [{'name': '知识库检索设置', 'params': [{'key': 'user_question', 'help': '当选择多个问题时，将会多次运行本节点，每次运行时从批量问题中取一项进行处理。', 'test': 'var', 'type': 'user_question', 'label': '用户问题', 'value': ['input_1fb3a.user_input'], 'varZh': {'input_1fb3a.user_input': '输入/user_input'}, 'global': 'self=user_prompt', 'linkage': 'output_user_input', 'required': True, 'placeholder': '请选择用户问题'}, {'key': 'knowledge', 'type': 'knowledge_select_multi', 'label': '检索范围', 'value': {'type': 'knowledge', 'value': [{'key': 124, 'label': '比亚迪-秦PLUS-车型知识库'}]}, 'required': True, 'placeholder': '请选择知识库'}, {'key': 'user_auth', 'help': '开启后，只会对用户有使用权限的知识库进行检索。', 'type': 'switch', 'label': '用户知识库权限校验', 'value': False}, {'key': 'max_chunk_size', 'help': '通过此参数控制最终传给模型的知识库检索结果文本长度，超过模型支持的最大上下文长度可能会导致报错。', 'type': 'number', 'label': '检索结果长度', 'value': 15000}, {'key': 'retrieved_result', 'type': 'var', 'label': '检索结果', 'global': 'self=user_prompt'}]}, {'name': 'AI回复生成设置', 'params': [{'key': 'system_prompt', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'required': True}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '用户问题：```{{#rag_63d89.user_question#}}```\n参考文本：```{{#rag_63d89.retrieved_result#}}```\n你的回答：', 'varZh': {'rag_63d89.user_question': 'user_question', 'rag_63d89.retrieved_result': 'retrieved_result'}, 'required': True}, {'key': 'model_id', 'type': 'bisheng_model', 'label': '模型', 'value': 82, 'required': True, 'placeholder': '请在模型管理中配置 LLM 模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output_user_input', 'help': '模型输出内容将会存储在该变量中。', 'type': 'var', 'label': '输出变量', 'value': [{'key': 'output_user_input', 'label': 'output_user_input'}], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1099}, 'position': {'x': 1575, 'y': 15}, 'selected': True}], 'edges': [{'id': 'xy-edge__input_1fb3aright_handle-rag_63d89left_handle', 'type': 'customEdge', 'source': 'input_1fb3a', 'target': 'rag_63d89', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__start_25626right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'start_25626', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__rag_63d89right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'rag_63d89', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': -31.242038216560445, 'y': 26.68152866242042, 'zoom': 0.6878980891719745}}}
[2025-08-20 20:22:07.259422] [INFO process-70468-49076 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 20:22:07.326874] [INFO process-70468-71932 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 20:22:08.330588] [INFO process-70468-49076 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-20 20:22:08.345869] [INFO process-70468-71932 bisheng.utils.threadpool:67] - trace=e307349e6ffe4ba0a7a228f4ecd34f0d async_task_added fun=wrapper_task args=e307349e6ffe4ba0a7a228f4ecd34f0d
[2025-08-20 20:22:21.683128] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=4d2a186949014c1cbf70186befd24fc5 trace_id=945e3686228d41b99fc7deba1fac6c4b message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': '你好，请问你知道什么车型？', 'dialog_files_content': []}, 'message': '你好，请问你知道什么车型？', 'message_id': '8a249c5908904ccb8c913b0e3a10eabe', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 20:22:21.712925] [INFO process-70468-71428 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 20:22:21.738470] [INFO process-70468-24660 bisheng.chat.clients.workflow_client:182] - trace=945e3686228d41b99fc7deba1fac6c4b get user input: {'input_1fb3a': {'data': {'user_input': '你好，请问你知道什么车型？', 'dialog_files_content': []}, 'message': '你好，请问你知道什么车型？', 'message_id': '8a249c5908904ccb8c913b0e3a10eabe', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 20:22:22.730516] [INFO process-70468-71428 bisheng.utils.threadpool:67] - trace=945e3686228d41b99fc7deba1fac6c4b async_task_added fun=wrapper_task args=945e3686228d41b99fc7deba1fac6c4b
[2025-08-20 20:23:27.682982] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=30e37ac341ae4079812a1031317a3bbe GET /api/v1/user/info
[2025-08-20 20:23:27.692848] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=964ad80b922940bf9403b16873a2f6d2 GET /api/v1/env
[2025-08-20 20:23:29.754205] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=30e37ac341ae4079812a1031317a3bbe GET /api/v1/user/info 200 timecost=2071.223
[2025-08-20 20:23:29.764914] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=964ad80b922940bf9403b16873a2f6d2 GET /api/v1/env 200 timecost=2073.069
[2025-08-20 20:23:29.773198] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7056e8a4addb4a71b46dcd263601f51b GET /api/v1/all
[2025-08-20 20:23:29.788205] [INFO process-70468-75076 bisheng.interface.custom.utils:343] - trace=7056e8a4addb4a71b46dcd263601f51b Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-20 20:23:29.919646] [INFO process-70468-75076 bisheng.interface.custom.utils:354] - trace=7056e8a4addb4a71b46dcd263601f51b Loading 1 component(s) from category custom_components
[2025-08-20 20:23:30.731179] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7056e8a4addb4a71b46dcd263601f51b GET /api/v1/all 200 timecost=957.981
[2025-08-20 20:23:30.744354] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=dd9f1439d53346ca95e080698eede922 GET /api/v1/component
[2025-08-20 20:23:30.757441] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f330d1e550d74171bc9a131742bbe11a GET /api/v1/skill/template
[2025-08-20 20:23:30.770077] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=eb2c80dfea6347b1a9c7214c4c2bb6cb GET /api/v1/tag
[2025-08-20 20:23:30.785838] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f3d8d18035e048489ac4fe4ab50797fd GET /api/v1/workstation/config
[2025-08-20 20:23:30.805457] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=687bd6a62d274d45b5264323452d2d97 GET /api/v1/workflow/list
[2025-08-20 20:23:31.635790] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=dd9f1439d53346ca95e080698eede922 GET /api/v1/component 200 timecost=891.437
[2025-08-20 20:23:31.962660] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=eb2c80dfea6347b1a9c7214c4c2bb6cb GET /api/v1/tag 200 timecost=1192.583
[2025-08-20 20:23:32.075325] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f330d1e550d74171bc9a131742bbe11a GET /api/v1/skill/template 200 timecost=1317.884
[2025-08-20 20:23:32.328124] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=687bd6a62d274d45b5264323452d2d97 GET /api/v1/workflow/list 200 timecost=1522.668
[2025-08-20 20:23:32.688548] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=eba9f2f744cf4ab288d7e5af886d92b4 GET /api/v1/skill/template
[2025-08-20 20:23:33.064267] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f3d8d18035e048489ac4fe4ab50797fd GET /api/v1/workstation/config 200 timecost=2278.429
[2025-08-20 20:23:33.180775] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7bc01ea306be4f049fc164ab5f29e040 GET /api/v1/llm/knowledge
[2025-08-20 20:23:33.401485] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=eba9f2f744cf4ab288d7e5af886d92b4 GET /api/v1/skill/template 200 timecost=712.937
[2025-08-20 20:23:33.478482] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f48e554bbcba469a8d19bfdf666c1b18 GET /api/v1/llm
[2025-08-20 20:23:34.802082] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7bc01ea306be4f049fc164ab5f29e040 GET /api/v1/llm/knowledge 200 timecost=1622.408
[2025-08-20 20:23:34.808077] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=791c6078a117406ea98f26f9c14cc092 GET /api/v1/knowledge
[2025-08-20 20:23:35.356597] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=791c6078a117406ea98f26f9c14cc092 GET /api/v1/knowledge 200 timecost=548.52
[2025-08-20 20:23:35.414236] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f48e554bbcba469a8d19bfdf666c1b18 GET /api/v1/llm 200 timecost=1936.8
[2025-08-20 20:23:35.426885] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=faba91c7935f4842a679bcdcbfc3ac53 GET /api/v1/llm
[2025-08-20 20:23:36.361194] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=faba91c7935f4842a679bcdcbfc3ac53 GET /api/v1/llm 200 timecost=934.309
[2025-08-20 20:23:36.681511] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7e3f155515bf4111ab1d541afab0ad19 GET /api/v1/llm
[2025-08-20 20:23:37.603024] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7e3f155515bf4111ab1d541afab0ad19 GET /api/v1/llm 200 timecost=922.555
[2025-08-20 20:26:52.239376] [INFO process-70468-67360 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1005, None)
[2025-08-20 20:26:52.248023] [INFO process-70468-67360 bisheng.chat.manager:181] - trace=1 close_client client_key=4d2a186949014c1cbf70186befd24fc5
[2025-08-20 20:26:52.258099] [ERROR process-70468-67360 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-20 20:26:52.265601] [ERROR process-70468-77596 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 12, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "4d2a186949014c1cbf70186befd24fc5"}', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'chat_id': ''} error: Cannot call "send" once a close message has been sent.
[2025-08-20 20:26:52.276915] [WARNING process-70468-67360 bisheng.chat.manager:179] - trace=1 close_client client_key=4d2a186949014c1cbf70186befd24fc5 not in active_clients
[2025-08-20 20:26:52.527518] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b8cff12220a043648d9aedfb15fa5d3e GET /api/v1/skill/template
[2025-08-20 20:26:53.475000] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=17b93933a27a42d49e7d841708b15cb4 GET /api/v1/workflow/list
[2025-08-20 20:26:53.704648] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b8cff12220a043648d9aedfb15fa5d3e GET /api/v1/skill/template 200 timecost=1177.13
[2025-08-20 20:26:54.355795] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=17b93933a27a42d49e7d841708b15cb4 GET /api/v1/workflow/list 200 timecost=880.794
[2025-08-20 20:26:54.425312] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=888eb0e514df475ca35df52318f86b2d GET /api/v1/skill/template
[2025-08-20 20:26:54.787603] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=888eb0e514df475ca35df52318f86b2d GET /api/v1/skill/template 200 timecost=362.29
[2025-08-20 20:26:57.525152] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3c02f3c8854e42b29bf77139a1725438 GET /api/v1/group/list
[2025-08-20 20:26:58.657083] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3c02f3c8854e42b29bf77139a1725438 GET /api/v1/group/list 200 timecost=1131.93
[2025-08-20 20:26:58.669656] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4e58afbdddc94b51bf2b6338756744f3 GET /api/v1/role/list
[2025-08-20 20:26:58.681773] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=4bf5176bd0ee489c8a49f56b0d9df196 GET /api/v1/user/list
[2025-08-20 20:27:10.611793] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4e58afbdddc94b51bf2b6338756744f3 GET /api/v1/role/list 200 timecost=11942.137
[2025-08-20 20:27:10.619316] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=4bf5176bd0ee489c8a49f56b0d9df196 GET /api/v1/user/list 201 timecost=11937.542
[2025-08-20 20:27:10.625893] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6e4f66edcc624387beaee469ecb1acc8 GET /api/v1/llm
[2025-08-20 20:27:10.976883] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6e4f66edcc624387beaee469ecb1acc8 GET /api/v1/llm 200 timecost=351.99
[2025-08-20 20:27:11.301871] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=19a895ae6f2448ada102c1fc4f26b32d GET /api/v1/llm
[2025-08-20 20:27:11.687877] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=19a895ae6f2448ada102c1fc4f26b32d GET /api/v1/llm 200 timecost=386.006
[2025-08-20 20:27:14.031177] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2c82424bffcd48718c93a3d98a45077f GET /api/v1/llm/workbench
[2025-08-20 20:27:14.535980] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2c82424bffcd48718c93a3d98a45077f GET /api/v1/llm/workbench 200 timecost=504.803
[2025-08-20 20:27:15.523024] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bbe9830ee0d04012ad4a80cac7556664 GET /api/v1/llm/knowledge
[2025-08-20 20:27:15.786707] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bbe9830ee0d04012ad4a80cac7556664 GET /api/v1/llm/knowledge 200 timecost=263.684
[2025-08-20 20:27:16.934067] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f6e499f9c2c64df2b5881c97891aee52 GET /api/v1/finetune/server/filters
[2025-08-20 20:27:16.940420] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=718e8560d7624e2282aedd349c5289e0 GET /api/v1/finetune/job
[2025-08-20 20:27:16.946752] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a74193d35f89478697502012529b6a6d GET /api/v1/server/list_server
[2025-08-20 20:27:18.105559] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f6e499f9c2c64df2b5881c97891aee52 GET /api/v1/finetune/server/filters 200 timecost=1170.492
[2025-08-20 20:27:18.116663] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=718e8560d7624e2282aedd349c5289e0 GET /api/v1/finetune/job 200 timecost=1177.243
[2025-08-20 20:27:18.128575] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a74193d35f89478697502012529b6a6d GET /api/v1/server/list_server 200 timecost=1181.823
[2025-08-20 20:27:18.140467] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ea47080532d342b393fe8e70f37b5edf GET /api/v1/llm
[2025-08-20 20:27:18.447210] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a767668db01e4c589b77760f8549c3a6 GET /api/v1/finetune/job
[2025-08-20 20:27:18.789755] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a767668db01e4c589b77760f8549c3a6 GET /api/v1/finetune/job 200 timecost=343.655
[2025-08-20 20:27:19.106471] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ea47080532d342b393fe8e70f37b5edf GET /api/v1/llm 200 timecost=966.004
[2025-08-20 20:27:19.956543] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e9e6e560a19b4557a9f551697b03bc7a GET /api/v1/group/list
[2025-08-20 20:27:19.965240] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ce6aa9d1f433430ab60910b156b72ed5 GET /api/v1/role/list
[2025-08-20 20:27:21.420710] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e9e6e560a19b4557a9f551697b03bc7a GET /api/v1/group/list 200 timecost=1464.167
[2025-08-20 20:27:21.432241] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ce6aa9d1f433430ab60910b156b72ed5 GET /api/v1/role/list 200 timecost=1467.001
[2025-08-20 20:27:21.442023] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d2d0e5a47f0044a6ac8b6b9a819ec266 GET /api/v1/user/list
[2025-08-20 20:27:35.140962] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d2d0e5a47f0044a6ac8b6b9a819ec266 GET /api/v1/user/list 201 timecost=13697.94
[2025-08-20 20:27:35.156371] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9a503f6c73fb4b1cb3507a0001446cb6 GET /api/v1/config
[2025-08-20 20:27:35.168945] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e0c03b46bc2147c2b0b84017c672c824 GET /api/v1/group/list
[2025-08-20 20:27:35.185252] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=beadc5d3a9f443ba9364f55d5978f441 GET /api/v1/role/list
[2025-08-20 20:27:35.206077] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e16c16b7121a40bab5ac91c853e3f242 GET /api/v1/llm
[2025-08-20 20:27:35.226084] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9a656f078aa64f32898853cb375aa055 GET /api/v1/skill/template
[2025-08-20 20:27:38.128183] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e0c03b46bc2147c2b0b84017c672c824 GET /api/v1/group/list 200 timecost=2959.238
[2025-08-20 20:27:38.140048] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=beadc5d3a9f443ba9364f55d5978f441 GET /api/v1/role/list 200 timecost=2954.796
[2025-08-20 20:27:38.309575] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ffd72dd20d8d4749b4b4f0b77f34c706 GET /api/v1/workflow/list
[2025-08-20 20:27:38.482245] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9c7f9df8316145c185feaf82ab5434c9 GET /api/v1/llm/knowledge
[2025-08-20 20:27:38.494769] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9a503f6c73fb4b1cb3507a0001446cb6 GET /api/v1/config 200 timecost=3339.415
[2025-08-20 20:27:38.669705] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bc0c350e3de84e63a84da8ce29152972 GET /api/v1/knowledge
[2025-08-20 20:27:38.848550] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b3856e1adb9b498b9647b8487a1e6d84 GET /api/v1/user/list
[2025-08-20 20:27:53.401062] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b3856e1adb9b498b9647b8487a1e6d84 GET /api/v1/user/list 201 timecost=14552.512
[2025-08-20 20:27:53.413184] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e16c16b7121a40bab5ac91c853e3f242 GET /api/v1/llm 200 timecost=18207.672
[2025-08-20 20:27:53.421207] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9a656f078aa64f32898853cb375aa055 GET /api/v1/skill/template 200 timecost=18195.122
[2025-08-20 20:27:53.430723] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9c7f9df8316145c185feaf82ab5434c9 GET /api/v1/llm/knowledge 200 timecost=14949.578
[2025-08-20 20:27:53.439554] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ffd72dd20d8d4749b4b4f0b77f34c706 GET /api/v1/workflow/list 200 timecost=15129.979
[2025-08-20 20:27:53.725112] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=233f19d5c758481cbb06dbf176c4a50c GET /api/v1/group/list
[2025-08-20 20:27:54.544400] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=233f19d5c758481cbb06dbf176c4a50c GET /api/v1/group/list 200 timecost=820.3
[2025-08-20 20:27:54.560921] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=75ec0f8b0faa45e6ab16d9ece1246df1 GET /api/v1/config
[2025-08-20 20:27:54.571457] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1d5d1e81e27647b48c282930ac1d38c2 GET /api/v1/group/list
[2025-08-20 20:27:54.584007] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1e63b4aadac94d1bb3db39fc9c7521cd GET /api/v1/group/list
[2025-08-20 20:27:54.599191] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b33864717f924f559befaa95e489d932 GET /api/v1/llm
[2025-08-20 20:27:57.327844] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1d5d1e81e27647b48c282930ac1d38c2 GET /api/v1/group/list 200 timecost=2756.386
[2025-08-20 20:27:57.338758] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1e63b4aadac94d1bb3db39fc9c7521cd GET /api/v1/group/list 200 timecost=2754.751
[2025-08-20 20:27:57.349270] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9b26cb474ff74b28b445f150f5f648f8 GET /api/v1/llm
[2025-08-20 20:27:57.531654] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=75ec0f8b0faa45e6ab16d9ece1246df1 GET /api/v1/config 200 timecost=2970.733
[2025-08-20 20:27:57.692057] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bc0c350e3de84e63a84da8ce29152972 GET /api/v1/knowledge 200 timecost=19022.353
[2025-08-20 20:27:57.704595] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b806b564330b49539b0715696d2d3b13 GET /api/v1/role/list
[2025-08-20 20:27:57.717649] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b7a52e36404b4b2eb2f353cabfc91fa1 GET /api/v1/user/list
[2025-08-20 20:28:06.457647] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b806b564330b49539b0715696d2d3b13 GET /api/v1/role/list 200 timecost=8753.052
[2025-08-20 20:28:06.470450] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b7a52e36404b4b2eb2f353cabfc91fa1 GET /api/v1/user/list 201 timecost=8752.802
[2025-08-20 20:28:06.482010] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b33864717f924f559befaa95e489d932 GET /api/v1/llm 200 timecost=11882.819
[2025-08-20 20:28:06.491600] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9b26cb474ff74b28b445f150f5f648f8 GET /api/v1/llm 200 timecost=9142.329
[2025-08-20 20:28:06.506281] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f051fd7da62a423ab15c537212b25290 GET /api/v1/group/list
[2025-08-20 20:28:06.522183] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8df2524e5b66446f85efc48d059fc281 GET /api/v1/user/admin
[2025-08-20 20:28:07.988852] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f051fd7da62a423ab15c537212b25290 GET /api/v1/group/list 200 timecost=1482.571
[2025-08-20 20:28:08.001502] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8df2524e5b66446f85efc48d059fc281 GET /api/v1/user/admin 200 timecost=1479.319
[2025-08-20 20:28:08.012705] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=43f352ebe70c4563b2adbd35796316bf GET /api/v1/audit/session
[2025-08-20 20:28:08.026535] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=03e9f1bdcad04f8888c6cb2d940decbe GET /api/v1/audit/operators
[2025-08-20 20:28:08.039171] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0dc5803e8e35474aa1ea49c017545632 GET /api/v1/audit
[2025-08-20 20:28:08.055035] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=43de54c8c39b469497da6e1e91882fc7 GET /api/v1/llm
[2025-08-20 20:28:09.805089] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=43de54c8c39b469497da6e1e91882fc7 GET /api/v1/llm 200 timecost=1750.054
[2025-08-20 20:28:10.283000] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0dc5803e8e35474aa1ea49c017545632 GET /api/v1/audit 200 timecost=2244.826
[2025-08-20 20:28:10.582866] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=03e9f1bdcad04f8888c6cb2d940decbe GET /api/v1/audit/operators 200 timecost=2556.331
[2025-08-20 20:28:15.280693] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=43f352ebe70c4563b2adbd35796316bf GET /api/v1/audit/session 200 timecost=7269.498
[2025-08-20 20:28:15.613135] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=efa59016dcdc4bb5992a198175a04113 GET /api/v1/audit/session
[2025-08-20 20:28:16.423006] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6df81e17bce945a38f8012f8979032ef GET /api/v1/llm/info
[2025-08-20 20:28:16.902908] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6df81e17bce945a38f8012f8979032ef GET /api/v1/llm/info 200 timecost=480.472
[2025-08-20 20:28:20.713843] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=efa59016dcdc4bb5992a198175a04113 GET /api/v1/audit/session 200 timecost=5100.708
[2025-08-20 20:28:24.145790] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ac58d26f38a147178953ec53dafc72ef GET /api/v1/finetune/server/filters
[2025-08-20 20:28:24.163683] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=da6f0be3114b4f80aa3840a07edb3a08 GET /api/v1/finetune/job
[2025-08-20 20:28:24.697306] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ce68318565014394abed805e3f4d570d GET /api/v1/server/list_server
[2025-08-20 20:28:24.706481] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ac58d26f38a147178953ec53dafc72ef GET /api/v1/finetune/server/filters 200 timecost=560.692
[2025-08-20 20:28:26.063712] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=da6f0be3114b4f80aa3840a07edb3a08 GET /api/v1/finetune/job 200 timecost=1900.029
[2025-08-20 20:28:26.073449] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ce68318565014394abed805e3f4d570d GET /api/v1/server/list_server 200 timecost=1376.143
[2025-08-20 20:28:26.086858] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f501f78924cc4ba4b1905df8620e4fcd GET /api/v1/llm
[2025-08-20 20:28:26.401934] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=892ca9436f494d16a95a99d9b4c046ba GET /api/v1/finetune/job
[2025-08-20 20:28:26.748426] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=892ca9436f494d16a95a99d9b4c046ba GET /api/v1/finetune/job 200 timecost=346.492
[2025-08-20 20:28:26.760930] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f501f78924cc4ba4b1905df8620e4fcd GET /api/v1/llm 200 timecost=675.072
[2025-08-20 20:28:44.505613] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e17151d23a0549df86ec27af757e23f9 GET /api/v1/web/config
[2025-08-20 20:28:44.897452] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e17151d23a0549df86ec27af757e23f9 GET /api/v1/web/config 200 timecost=391.839
[2025-08-20 20:28:47.980325] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0af23883e7d349249f30e0125c6ee503 GET /api/v1/tag
[2025-08-20 20:28:47.996550] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ca335fed204d490aaaa694b8861606a1 GET /api/v1/tag/home
[2025-08-20 20:28:48.016368] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=86a2635d95d94eeb91227ac866d09721 GET /api/v1/chat/list
[2025-08-20 20:28:49.319352] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9099341dd81c488bab90edb54eb5129a GET /api/v1/chat/online
[2025-08-20 20:28:50.139235] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0af23883e7d349249f30e0125c6ee503 GET /api/v1/tag 200 timecost=2158.91
[2025-08-20 20:28:50.152722] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ca335fed204d490aaaa694b8861606a1 GET /api/v1/tag/home 200 timecost=2156.172
[2025-08-20 20:28:50.173765] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1dd96552a81644f49d274dd52d023fe6 GET /api/v1/user/info
[2025-08-20 20:28:50.192518] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a14ed04afcb941e991eb1d7f1eed7f01 GET /api/v1/env
[2025-08-20 20:28:51.626278] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1dd96552a81644f49d274dd52d023fe6 GET /api/v1/user/info 200 timecost=1452.513
[2025-08-20 20:28:51.645873] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=86a2635d95d94eeb91227ac866d09721 GET /api/v1/chat/list 200 timecost=3629.504
[2025-08-20 20:28:51.659629] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9099341dd81c488bab90edb54eb5129a GET /api/v1/chat/online 200 timecost=2340.277
[2025-08-20 20:28:51.673170] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6189c5c60f774141ad221bd8bfe9050d GET /api/v1/all
[2025-08-20 20:28:51.690370] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9a70659303f041ae8d25816f88cbb5dd GET /api/v1/tag
[2025-08-20 20:28:51.722604] [INFO process-70468-63740 bisheng.interface.custom.utils:343] - trace=6189c5c60f774141ad221bd8bfe9050d Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-20 20:28:51.979827] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=042809b8f81c417889ec48659b944ebf GET /api/v1/component
[2025-08-20 20:28:52.264211] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8f6cd43f71214e44af25b190b560f65f GET /api/v1/tag/home
[2025-08-20 20:28:52.285769] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ff254c44897e4166b0335068fed4b5eb GET /api/v1/chat/list
[2025-08-20 20:28:52.548730] [INFO process-70468-63740 bisheng.interface.custom.utils:354] - trace=6189c5c60f774141ad221bd8bfe9050d Loading 1 component(s) from category custom_components
[2025-08-20 20:28:52.722852] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9a70659303f041ae8d25816f88cbb5dd GET /api/v1/tag 200 timecost=1032.482
[2025-08-20 20:28:52.866705] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=042809b8f81c417889ec48659b944ebf GET /api/v1/component 200 timecost=887.85
[2025-08-20 20:28:53.789803] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a14ed04afcb941e991eb1d7f1eed7f01 GET /api/v1/env 200 timecost=3598.286
[2025-08-20 20:28:53.801251] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6189c5c60f774141ad221bd8bfe9050d GET /api/v1/all 200 timecost=2128.596
[2025-08-20 20:28:53.814292] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8f6cd43f71214e44af25b190b560f65f GET /api/v1/tag/home 200 timecost=1550.597
[2025-08-20 20:28:53.826747] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=88a2b8da3398413390e1cf123eb22d42 GET /api/v1/chat/online
[2025-08-20 20:28:54.102519] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3ab1a5f1440b4e0bbdbad238296dd1b2 GET /api/v1/tag
[2025-08-20 20:28:54.276350] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9fe161771a56466ea5c7d2c0bf16762b GET /api/v1/workstation/config
[2025-08-20 20:28:54.710696] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ff254c44897e4166b0335068fed4b5eb GET /api/v1/chat/list 200 timecost=2424.927
[2025-08-20 20:28:55.095923] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=88a2b8da3398413390e1cf123eb22d42 GET /api/v1/chat/online 200 timecost=1269.176
[2025-08-20 20:28:55.688054] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3ab1a5f1440b4e0bbdbad238296dd1b2 GET /api/v1/tag 200 timecost=1585.535
[2025-08-20 20:28:55.996091] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9fe161771a56466ea5c7d2c0bf16762b GET /api/v1/workstation/config 200 timecost=1719.741
[2025-08-20 20:28:56.448498] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=82db3ffb9058470ea2d5bf69d6c8dbec GET /api/v1/skill/template
[2025-08-20 20:28:57.022557] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=82db3ffb9058470ea2d5bf69d6c8dbec GET /api/v1/skill/template 200 timecost=574.059
[2025-08-20 20:28:57.041323] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=36a12df8220f4be5bbc3cf31b74e268f GET /api/v1/workflow/list
[2025-08-20 20:28:57.448886] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=88d740c5e1d84d1289e85cb4649c0602 GET /api/v1/llm/knowledge
[2025-08-20 20:28:57.458426] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1a6ab35c574e4dc8a8282cc133aaffad GET /api/v1/llm
[2025-08-20 20:28:57.928806] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=88d740c5e1d84d1289e85cb4649c0602 GET /api/v1/llm/knowledge 200 timecost=479.921
[2025-08-20 20:28:58.030895] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2ab6e77cc89e45c9ac491a873f7a2d24 GET /api/v1/knowledge
[2025-08-20 20:28:58.415043] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1a6ab35c574e4dc8a8282cc133aaffad GET /api/v1/llm 200 timecost=957.616
[2025-08-20 20:28:59.111675] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2ab6e77cc89e45c9ac491a873f7a2d24 GET /api/v1/knowledge 200 timecost=1079.782
[2025-08-20 20:28:59.123709] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=36a12df8220f4be5bbc3cf31b74e268f GET /api/v1/workflow/list 200 timecost=2083.387
[2025-08-20 20:28:59.138924] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=230ca46f46e145369533db8a10651e7d GET /api/v1/skill/template
[2025-08-20 20:28:59.482803] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=230ca46f46e145369533db8a10651e7d GET /api/v1/skill/template 200 timecost=343.878
[2025-08-20 20:28:59.495692] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f6aaa852697e47f09f9b56495afb3c9b GET /api/v1/workflow/list
[2025-08-20 20:28:59.766573] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0adc756229e9490d935978dcbae33c53 GET /api/v1/tag
[2025-08-20 20:28:59.779194] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f017d0a2cb1b437580c59c6cd6735ec9 GET /api/v1/tag/home
[2025-08-20 20:28:59.792816] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8ee4c06e786a4c378f28ae80450ec2c2 GET /api/v1/chat/list
[2025-08-20 20:29:00.662670] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bd89f2e1aec5438da99d487bed340031 GET /api/v1/chat/online
[2025-08-20 20:29:00.918160] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=76959a76183f42a983c44a23cd3f9168 GET /api/v1/llm
[2025-08-20 20:29:01.141219] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f017d0a2cb1b437580c59c6cd6735ec9 GET /api/v1/tag/home 200 timecost=1362.025
[2025-08-20 20:29:01.152756] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0adc756229e9490d935978dcbae33c53 GET /api/v1/tag 200 timecost=1386.183
[2025-08-20 20:29:01.647424] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f6aaa852697e47f09f9b56495afb3c9b GET /api/v1/workflow/list 200 timecost=2152.727
[2025-08-20 20:29:01.683636] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=76959a76183f42a983c44a23cd3f9168 GET /api/v1/llm 200 timecost=765.476
[2025-08-20 20:29:01.764949] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8ee4c06e786a4c378f28ae80450ec2c2 GET /api/v1/chat/list 200 timecost=1972.134
[2025-08-20 20:29:02.472679] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bd89f2e1aec5438da99d487bed340031 GET /api/v1/chat/online 200 timecost=1810.009
[2025-08-20 20:29:24.177207] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=70de3bd019744d7c9306abee11869c7d GET /api/v1/llm
[2025-08-20 20:29:24.642356] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=70de3bd019744d7c9306abee11869c7d GET /api/v1/llm 200 timecost=465.148
[2025-08-20 20:29:26.977066] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e207292aba9a4b01b4d6e88652cc610b GET /api/v1/llm/workbench
[2025-08-20 20:29:27.559323] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e207292aba9a4b01b4d6e88652cc610b GET /api/v1/llm/workbench 200 timecost=582.257
[2025-08-20 20:30:31.154415] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=55ecacc38fde47d79449bdf90c9732c0 GET /api/v1/llm/workbench
[2025-08-20 20:30:32.629284] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=55ecacc38fde47d79449bdf90c9732c0 GET /api/v1/llm/workbench 200 timecost=1475.885
[2025-08-20 20:30:39.905673] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=412a991b5cdc4f58b74f4b46d557a94f GET /api/v1/llm/workflow
[2025-08-20 20:30:40.467373] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=412a991b5cdc4f58b74f4b46d557a94f GET /api/v1/llm/workflow 200 timecost=561.7
[2025-08-20 20:30:48.285337] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ea989dd8e41d47ddb399b113c9ab8e22 GET /api/v1/llm/workbench
[2025-08-20 20:30:48.602922] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ea989dd8e41d47ddb399b113c9ab8e22 GET /api/v1/llm/workbench 200 timecost=318.623
[2025-08-20 20:30:49.219236] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=40b1632cfc994621b727ace2b3b634fa GET /api/v1/llm/knowledge
[2025-08-20 20:30:49.750073] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=40b1632cfc994621b727ace2b3b634fa GET /api/v1/llm/knowledge 200 timecost=530.837
[2025-08-20 20:30:51.766513] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=485e9fedfc6147c984193f226a139032 GET /api/v1/llm/assistant
[2025-08-20 20:30:52.612256] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=485e9fedfc6147c984193f226a139032 GET /api/v1/llm/assistant 200 timecost=845.743
[2025-08-20 20:30:53.863370] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=509445a27db84e72bc063909daa7b0c7 GET /api/v1/llm/evaluation
[2025-08-20 20:30:54.147686] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=509445a27db84e72bc063909daa7b0c7 GET /api/v1/llm/evaluation 200 timecost=285.328
[2025-08-20 20:31:00.346290] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=32a9bcc8dae44dcda6c9ff7054818199 GET /api/v1/llm/workbench
[2025-08-20 20:31:00.881544] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=32a9bcc8dae44dcda6c9ff7054818199 GET /api/v1/llm/workbench 200 timecost=535.254
[2025-08-20 20:31:19.766066] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=00a96403c820486fbb4fe1aa40d61dc2 GET /api/v1/llm/workflow
[2025-08-20 20:31:20.301896] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=00a96403c820486fbb4fe1aa40d61dc2 GET /api/v1/llm/workflow 200 timecost=535.829
[2025-08-20 20:31:32.048328] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=2501864d69904a9eb31cad20996a99ac GET /api/v1/llm/info
[2025-08-20 20:31:32.465569] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=2501864d69904a9eb31cad20996a99ac GET /api/v1/llm/info 200 timecost=417.241
[2025-08-20 20:33:41.162317] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=71c07a64a7f6407f9b283c9db2884b90 GET /api/v1/skill/template
[2025-08-20 20:33:41.173992] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=0e4604ef56b94c6b95e688fc8788a343 GET /api/v1/tag
[2025-08-20 20:33:41.717246] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=195f5e4782484ea8bc7e108a45916362 GET /api/v1/workflow/list
[2025-08-20 20:33:41.918584] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=feb343cb205b4f4a8e7a8610f8887724 GET /api/v1/llm/knowledge
[2025-08-20 20:33:41.936374] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=eb67e500eb834d8b9cc076111f10cc2a GET /api/v1/llm
[2025-08-20 20:33:41.956704] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=71c07a64a7f6407f9b283c9db2884b90 GET /api/v1/skill/template 200 timecost=794.387
[2025-08-20 20:33:42.294380] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=0e4604ef56b94c6b95e688fc8788a343 GET /api/v1/tag 200 timecost=1120.388
[2025-08-20 20:33:42.463741] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=cbd06cfa30844843916678e7e4341511 GET /api/v1/knowledge
[2025-08-20 20:33:42.653190] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=eb67e500eb834d8b9cc076111f10cc2a GET /api/v1/llm 200 timecost=716.816
[2025-08-20 20:33:42.926560] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=feb343cb205b4f4a8e7a8610f8887724 GET /api/v1/llm/knowledge 200 timecost=1007.976
[2025-08-20 20:33:43.694075] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=cbd06cfa30844843916678e7e4341511 GET /api/v1/knowledge 200 timecost=1230.334
[2025-08-20 20:33:43.704233] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=195f5e4782484ea8bc7e108a45916362 GET /api/v1/workflow/list 200 timecost=1986.986
[2025-08-20 20:33:49.637963] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1f73dc9cc3584a179ec08c521f62eceb GET /api/v1/knowledge
[2025-08-20 20:33:50.445756] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1f73dc9cc3584a179ec08c521f62eceb GET /api/v1/knowledge 200 timecost=807.793
[2025-08-20 20:33:56.065195] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=b466c53575544dd19143729730bb595d GET /api/v1/knowledge
[2025-08-20 20:33:57.381866] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=b466c53575544dd19143729730bb595d GET /api/v1/knowledge 200 timecost=1316.671
[2025-08-20 20:34:03.304950] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=523a73e57591439c99f226d07c1279d5 GET /api/v1/knowledge
[2025-08-20 20:34:03.925716] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=523a73e57591439c99f226d07c1279d5 GET /api/v1/knowledge 200 timecost=621.766
[2025-08-20 20:34:09.547278] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e660432e96394fc39f1ccd3f1919ba86 GET /api/v1/knowledge
[2025-08-20 20:34:10.389418] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e660432e96394fc39f1ccd3f1919ba86 GET /api/v1/knowledge 200 timecost=842.14
[2025-08-20 20:34:16.322277] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e454e1f155e84879a05cf68e19fc1be2 GET /api/v1/knowledge
[2025-08-20 20:34:16.829081] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e454e1f155e84879a05cf68e19fc1be2 GET /api/v1/knowledge 200 timecost=507.886
[2025-08-20 20:34:22.448255] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6a880acbae7d4697b2260ceb1b29dce7 GET /api/v1/knowledge
[2025-08-20 20:34:22.992103] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6a880acbae7d4697b2260ceb1b29dce7 GET /api/v1/knowledge 200 timecost=543.848
[2025-08-20 20:34:28.931008] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d20f6ec3dbd9481686a3d515f9346222 GET /api/v1/knowledge
[2025-08-20 20:34:29.754206] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d20f6ec3dbd9481686a3d515f9346222 GET /api/v1/knowledge 200 timecost=824.618
[2025-08-20 20:34:35.378669] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=ca2c08473c7e49f2a30b5fdfd1441d7b GET /api/v1/knowledge
[2025-08-20 20:34:36.925701] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=ca2c08473c7e49f2a30b5fdfd1441d7b GET /api/v1/knowledge 200 timecost=1547.032
[2025-08-20 20:34:43.327504] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1ebd20e2d2544bef99b89a3dff3d74d6 GET /api/v1/knowledge
[2025-08-20 20:34:44.394241] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1ebd20e2d2544bef99b89a3dff3d74d6 GET /api/v1/knowledge 200 timecost=1066.737
[2025-08-20 20:34:50.036051] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=994827cebbb64e4bbf32a6092dd51f5e GET /api/v1/knowledge
[2025-08-20 20:34:50.815760] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=994827cebbb64e4bbf32a6092dd51f5e GET /api/v1/knowledge 200 timecost=779.709
[2025-08-20 20:34:56.760824] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=7eee92aa9bf54fe1a77c4df2938553d0 GET /api/v1/knowledge
[2025-08-20 20:34:58.323506] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=7eee92aa9bf54fe1a77c4df2938553d0 GET /api/v1/knowledge 200 timecost=1562.682
[2025-08-20 20:35:03.943856] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=75d6935c64b24393a50894f00a4c2842 GET /api/v1/knowledge
[2025-08-20 20:35:04.963349] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=75d6935c64b24393a50894f00a4c2842 GET /api/v1/knowledge 200 timecost=1019.492
[2025-08-20 20:35:10.907173] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e1de49672c1f4a15af0d6b315b0a78bd GET /api/v1/knowledge
[2025-08-20 20:35:12.209035] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e1de49672c1f4a15af0d6b315b0a78bd GET /api/v1/knowledge 200 timecost=1302.882
[2025-08-20 20:35:17.829398] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9cc61c30f09440a38ae2aca2e53aaf97 GET /api/v1/knowledge
[2025-08-20 20:35:18.856144] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9cc61c30f09440a38ae2aca2e53aaf97 GET /api/v1/knowledge 200 timecost=1026.747
[2025-08-20 20:35:24.779546] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8a269864ee744480a7f27ab5f6b4ba33 GET /api/v1/knowledge
[2025-08-20 20:35:25.803919] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8a269864ee744480a7f27ab5f6b4ba33 GET /api/v1/knowledge 200 timecost=1024.373
[2025-08-20 20:35:31.422084] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=19d151652a0f44228943223b7a44f040 GET /api/v1/knowledge
[2025-08-20 20:35:32.189436] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=19d151652a0f44228943223b7a44f040 GET /api/v1/knowledge 200 timecost=767.352
[2025-08-20 20:35:38.130855] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d570c944e3634f829301efc63454668d GET /api/v1/knowledge
[2025-08-20 20:35:38.899273] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d570c944e3634f829301efc63454668d GET /api/v1/knowledge 200 timecost=768.419
[2025-08-20 20:35:44.526249] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=45144b60470b44358b052eb6ab246c64 GET /api/v1/knowledge
[2025-08-20 20:35:45.585477] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=45144b60470b44358b052eb6ab246c64 GET /api/v1/knowledge 200 timecost=1058.233
[2025-08-20 20:35:51.514553] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d2320025be1943219402717b2247e996 GET /api/v1/knowledge
[2025-08-20 20:35:52.048653] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d2320025be1943219402717b2247e996 GET /api/v1/knowledge 200 timecost=534.1
[2025-08-20 20:35:57.671729] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=985d0b452e654712a5565c8166bfc6e5 GET /api/v1/knowledge
[2025-08-20 20:35:58.698119] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=985d0b452e654712a5565c8166bfc6e5 GET /api/v1/knowledge 200 timecost=1026.39
[2025-08-20 20:36:04.633387] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3a3cfb3efa5c4d2997b9440cd4f924d8 GET /api/v1/knowledge
[2025-08-20 20:36:05.682615] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3a3cfb3efa5c4d2997b9440cd4f924d8 GET /api/v1/knowledge 200 timecost=1048.292
[2025-08-20 20:36:11.301945] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=3d6da40c1d094c34aa9e4973e598a0a6 GET /api/v1/knowledge
[2025-08-20 20:36:12.338911] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=3d6da40c1d094c34aa9e4973e598a0a6 GET /api/v1/knowledge 200 timecost=1036.966
[2025-08-20 20:36:12.968758] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=9d137ed7384c4752b7e4312dc8671270 GET /api/v1/llm
[2025-08-20 20:36:13.669372] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=9d137ed7384c4752b7e4312dc8671270 GET /api/v1/llm 200 timecost=700.613
[2025-08-20 20:36:19.220761] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=16bd40e38327414fa5244fbacad14328 GET /api/v1/knowledge
[2025-08-20 20:36:20.071532] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=16bd40e38327414fa5244fbacad14328 GET /api/v1/knowledge 200 timecost=850.771
[2025-08-20 20:39:07.637069] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=dc78ed7e7faf46d79655961c044d936f GET /api/v1/llm
[2025-08-20 20:39:08.281565] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=dc78ed7e7faf46d79655961c044d936f GET /api/v1/llm 200 timecost=644.495
[2025-08-20 20:39:44.661682] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d1980a68d5ca4ff3ba4dfb8996180a94 GET /api/v1/skill/template
[2025-08-20 20:39:44.969839] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e1ec1ff625e04b9e9831720f6e621f77 GET /api/v1/tag
[2025-08-20 20:39:45.198267] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d1980a68d5ca4ff3ba4dfb8996180a94 GET /api/v1/skill/template 200 timecost=536.586
[2025-08-20 20:39:45.551084] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1a698d46781f4e3b930a7c1bd51f2abe GET /api/v1/workflow/list
[2025-08-20 20:39:45.695938] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e1ec1ff625e04b9e9831720f6e621f77 GET /api/v1/tag 200 timecost=726.098
[2025-08-20 20:39:47.150461] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1a698d46781f4e3b930a7c1bd51f2abe GET /api/v1/workflow/list 200 timecost=1600.939
[2025-08-20 20:39:47.497718] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=dd21f84bed0a4ae4a38585f3d860d908 GET /api/v1/skill/template
[2025-08-20 20:39:47.968073] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=dd21f84bed0a4ae4a38585f3d860d908 GET /api/v1/skill/template 200 timecost=470.355
[2025-08-20 20:52:11.369789] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=fd53718eee3f4012b82d3dabc3c4f74c GET /api/v1/llm
[2025-08-20 20:52:12.659482] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=fd53718eee3f4012b82d3dabc3c4f74c GET /api/v1/llm 200 timecost=1289.692
[2025-08-20 20:52:12.699585] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=45e6844376854103930db77c85db81e8 GET /api/v1/llm
[2025-08-20 20:52:13.326497] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=45e6844376854103930db77c85db81e8 GET /api/v1/llm 200 timecost=627.919
[2025-08-20 20:52:26.968461] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8bc577b7c7fd47be9b627b9f65399f8b GET /api/v1/llm
[2025-08-20 20:52:27.437535] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8bc577b7c7fd47be9b627b9f65399f8b GET /api/v1/llm 200 timecost=469.074
[2025-08-20 20:53:25.626015] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=d155b6da0f034b9f9c451bf5954314f9 GET /api/v1/llm/info
[2025-08-20 20:53:26.547277] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=d155b6da0f034b9f9c451bf5954314f9 GET /api/v1/llm/info 200 timecost=921.263
[2025-08-20 20:54:20.742912] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f0d79fe776b44ea48508a87f9e9c4173 GET /api/v1/llm/info
[2025-08-20 20:54:21.416532] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f0d79fe776b44ea48508a87f9e9c4173 GET /api/v1/llm/info 200 timecost=673.62
[2025-08-20 20:58:53.463229] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=91043f924c63496da6c00df6244cd736 GET /api/v1/llm
[2025-08-20 20:58:54.224291] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=91043f924c63496da6c00df6244cd736 GET /api/v1/llm 200 timecost=761.063
[2025-08-20 21:00:34.491429] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f2e972447a8e45b087c7886bf7585650 GET /api/v1/llm
[2025-08-20 21:04:05.645716] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f2e972447a8e45b087c7886bf7585650 GET /api/v1/llm 200 timecost=211154.288
[2025-08-20 21:04:15.157063] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f43a130661504d2296a906f2229436ac GET /api/v1/llm
[2025-08-20 21:04:15.613997] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f43a130661504d2296a906f2229436ac GET /api/v1/llm 200 timecost=456.934
[2025-08-20 21:04:19.528895] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6676338a302b4400a570ae5e9ec2ee3f GET /api/v1/skill/template
[2025-08-20 21:04:19.821714] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6676338a302b4400a570ae5e9ec2ee3f GET /api/v1/skill/template 200 timecost=292.818
[2025-08-20 21:04:19.832748] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=8c4a080924d943e68eef598497a2044e GET /api/v1/tag
[2025-08-20 21:04:20.263744] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=8c4a080924d943e68eef598497a2044e GET /api/v1/tag 200 timecost=430.997
[2025-08-20 21:04:20.422554] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f50af500e62947e797d9266aa8e95cd5 GET /api/v1/workflow/list
[2025-08-20 21:04:22.818537] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f50af500e62947e797d9266aa8e95cd5 GET /api/v1/workflow/list 200 timecost=2395.983
[2025-08-20 21:04:23.227736] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1cdd8c7313b5452ab0224080d53d2f4f GET /api/v1/skill/template
[2025-08-20 21:04:23.778709] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1cdd8c7313b5452ab0224080d53d2f4f GET /api/v1/skill/template 200 timecost=550.973
[2025-08-20 21:05:05.245913] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=bd2b402c5d7342c5a294eaf7deedf0f8 GET /api/v1/tag
[2025-08-20 21:05:05.559269] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e71f625fcfd24eafb5dfc63d8586c317 GET /api/v1/tag/home
[2025-08-20 21:05:05.565798] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f80d2eb9ffa749198510c4d380bcdb0b GET /api/v1/chat/list
[2025-08-20 21:05:05.805001] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=bd2b402c5d7342c5a294eaf7deedf0f8 GET /api/v1/tag 200 timecost=559.088
[2025-08-20 21:05:05.834853] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=1db016b8aa0a49e697e6fba1207af97c GET /api/v1/chat/online
[2025-08-20 21:05:06.033021] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=a78d0d4d05ee4de0a855b093137ef2a0 GET /api/v1/skill/template
[2025-08-20 21:05:06.363850] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e71f625fcfd24eafb5dfc63d8586c317 GET /api/v1/tag/home 200 timecost=804.581
[2025-08-20 21:05:06.550227] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=a78d0d4d05ee4de0a855b093137ef2a0 GET /api/v1/skill/template 200 timecost=517.207
[2025-08-20 21:05:06.844551] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=6a284116735942a88281b6f6a1f17ad4 GET /api/v1/workflow/list
[2025-08-20 21:05:07.233327] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f80d2eb9ffa749198510c4d380bcdb0b GET /api/v1/chat/list 200 timecost=1667.529
[2025-08-20 21:05:08.075840] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=1db016b8aa0a49e697e6fba1207af97c GET /api/v1/chat/online 200 timecost=2240.988
[2025-08-20 21:05:08.618395] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=6a284116735942a88281b6f6a1f17ad4 GET /api/v1/workflow/list 200 timecost=1773.844
[2025-08-20 21:05:08.984411] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=30d87b6eca3b4e6db6fdc1fc5c3a1950 GET /api/v1/skill/template
[2025-08-20 21:05:09.462882] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=30d87b6eca3b4e6db6fdc1fc5c3a1950 GET /api/v1/skill/template 200 timecost=479.979
[2025-08-20 21:05:13.667245] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=295a10dc9bcf486b8aaf83d25612c597 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627
[2025-08-20 21:05:14.161028] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=295a10dc9bcf486b8aaf83d25612c597 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627 200 timecost=493.783
[2025-08-20 21:05:14.637998] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=5728feeab38e4bfea0a412cff2da7f9e GET /api/v1/workflow/versions
[2025-08-20 21:05:14.957554] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=51a75468cef748f0b60131867e9cb7af GET /api/v1/assistant/tool_list
[2025-08-20 21:05:14.977197] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=38f292e4a0c5474aafc28300ad8ae124 GET /api/v1/knowledge
[2025-08-20 21:05:14.989758] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=801aeb6b4a0248a681f7ee2ab97606e5 GET /api/v1/llm
[2025-08-20 21:05:15.010306] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=f0d86be8be024df2b36228d88977db7f GET /api/v1/llm/workflow
[2025-08-20 21:05:15.787420] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=5728feeab38e4bfea0a412cff2da7f9e GET /api/v1/workflow/versions 200 timecost=1149.422
[2025-08-20 21:05:16.044751] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=801aeb6b4a0248a681f7ee2ab97606e5 GET /api/v1/llm 200 timecost=1054.993
[2025-08-20 21:05:16.150803] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=f0d86be8be024df2b36228d88977db7f GET /api/v1/llm/workflow 200 timecost=1141.414
[2025-08-20 21:05:16.659788] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=38f292e4a0c5474aafc28300ad8ae124 GET /api/v1/knowledge 200 timecost=1682.591
[2025-08-20 21:05:17.221566] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=51a75468cef748f0b60131867e9cb7af GET /api/v1/assistant/tool_list 200 timecost=2264.013
[2025-08-20 21:05:17.309917] [INFO process-70468-67360 bisheng.utils.http_middleware:21] - trace=e55ccbeac53b4dcca06453ff762f4daa GET /api/v1/knowledge/info
[2025-08-20 21:05:19.106307] [INFO process-70468-67360 bisheng.utils.http_middleware:24] - trace=e55ccbeac53b4dcca06453ff762f4daa GET /api/v1/knowledge/info 200 timecost=1796.39
[2025-08-20 21:05:19.477882] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=2d95dc6ca25043dfad83193baa49440b trace_id=dc66e06879454d938a1b3ad1a68e7cbb message={'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id': 12, 'flow_type': 10, 'update_time': '2025-08-07T15:12:45', 'id': '0c26e44b161448ca913617e3c857d627', 'status': 2, 'description': '', 'guide_word': None, 'create_time': '2025-07-31T15:46:27', 'nodes': [{'id': 'start_25626', 'data': {'v': '1', 'id': 'start_25626', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': [''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': '1afc3c', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 614}, 'position': {'x': 720, 'y': 20}, 'selected': False}, {'id': 'input_1fb3a', 'data': {'v': '2', 'id': 'input_1fb3a', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '输入文本内容', 'global': 'key'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 421}, 'position': {'x': 1155, 'y': 15}, 'selected': False}, {'id': 'rag_63d89', 'data': {'v': '1', 'id': 'rag_63d89', 'name': '文档知识库问答', 'type': 'rag', 'description': '根据用户问题从知识库中检索相关内容，结合检索结果调用大模型生成最终结果，支持多个问题并行执行。', 'group_params': [{'name': '知识库检索设置', 'params': [{'key': 'user_question', 'help': '当选择多个问题时，将会多次运行本节点，每次运行时从批量问题中取一项进行处理。', 'test': 'var', 'type': 'user_question', 'label': '用户问题', 'value': ['input_1fb3a.user_input'], 'varZh': {'input_1fb3a.user_input': '输入/user_input'}, 'global': 'self=user_prompt', 'linkage': 'output_user_input', 'required': True, 'placeholder': '请选择用户问题'}, {'key': 'knowledge', 'type': 'knowledge_select_multi', 'label': '检索范围', 'value': {'type': 'knowledge', 'value': [{'key': 124, 'label': '比亚迪-秦PLUS-车型知识库'}]}, 'required': True, 'placeholder': '请选择知识库'}, {'key': 'user_auth', 'help': '开启后，只会对用户有使用权限的知识库进行检索。', 'type': 'switch', 'label': '用户知识库权限校验', 'value': False}, {'key': 'max_chunk_size', 'help': '通过此参数控制最终传给模型的知识库检索结果文本长度，超过模型支持的最大上下文长度可能会导致报错。', 'type': 'number', 'label': '检索结果长度', 'value': 15000}, {'key': 'retrieved_result', 'type': 'var', 'label': '检索结果', 'global': 'self=user_prompt'}]}, {'name': 'AI回复生成设置', 'params': [{'key': 'system_prompt', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'required': True}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '用户问题：```{{#rag_63d89.user_question#}}```\n参考文本：```{{#rag_63d89.retrieved_result#}}```\n你的回答：', 'varZh': {'rag_63d89.user_question': 'user_question', 'rag_63d89.retrieved_result': 'retrieved_result'}, 'required': True}, {'key': 'model_id', 'type': 'bisheng_model', 'label': '模型', 'value': 82, 'required': True, 'placeholder': '请在模型管理中配置 LLM 模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output_user_input', 'help': '模型输出内容将会存储在该变量中。', 'type': 'var', 'label': '输出变量', 'value': [{'key': 'output_user_input', 'label': 'output_user_input'}], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1097}, 'position': {'x': 1575, 'y': 15}, 'selected': True}], 'edges': [{'id': 'xy-edge__input_1fb3aright_handle-rag_63d89left_handle', 'type': 'customEdge', 'source': 'input_1fb3a', 'target': 'rag_63d89', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__start_25626right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'start_25626', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__rag_63d89right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'rag_63d89', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 191.271194165907, 'y': 18.122151321786703, 'zoom': 0.4585232452142206}}}
[2025-08-20 21:05:19.495912] [INFO process-70468-49076 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:05:19.570243] [INFO process-70468-72480 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:05:20.570119] [INFO process-70468-49076 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-20 21:05:20.585326] [INFO process-70468-72480 bisheng.utils.threadpool:67] - trace=dc66e06879454d938a1b3ad1a68e7cbb async_task_added fun=wrapper_task args=dc66e06879454d938a1b3ad1a68e7cbb
[2025-08-20 21:05:29.379741] [INFO process-70468-67360 bisheng.chat.clients.base:68] - trace=1 client_id=2d95dc6ca25043dfad83193baa49440b trace_id=871bff3555c44e3d9231986d1bac151b message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': '你好，你知道什么车型？尽量详细一点。', 'dialog_files_content': []}, 'message': '你好，你知道什么车型？尽量详细一点。', 'message_id': '318efbc69f2449ae8d10ed457b1fc892', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 21:05:29.398923] [INFO process-70468-71428 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:05:29.414968] [INFO process-70468-79668 bisheng.chat.clients.workflow_client:182] - trace=871bff3555c44e3d9231986d1bac151b get user input: {'input_1fb3a': {'data': {'user_input': '你好，你知道什么车型？尽量详细一点。', 'dialog_files_content': []}, 'message': '你好，你知道什么车型？尽量详细一点。', 'message_id': '318efbc69f2449ae8d10ed457b1fc892', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 21:05:30.421448] [INFO process-70468-71428 bisheng.utils.threadpool:67] - trace=871bff3555c44e3d9231986d1bac151b async_task_added fun=wrapper_task args=871bff3555c44e3d9231986d1bac151b
[2025-08-20 21:07:19.936244] [INFO process-70468-67360 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1012, None)
[2025-08-20 21:07:19.943783] [INFO process-70468-67360 bisheng.chat.manager:181] - trace=1 close_client client_key=2d95dc6ca25043dfad83193baa49440b
[2025-08-20 21:07:20.117178] [ERROR process-70468-67124 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 12, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "2d95dc6ca25043dfad83193baa49440b"}', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'chat_id': ''} error: Unexpected ASGI message 'websocket.send', after sending 'websocket.close'.
[2025-08-20 21:07:20.202190] [ERROR process-70468-67360 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-20 21:07:20.206186] [WARNING process-70468-67360 bisheng.chat.manager:179] - trace=1 close_client client_key=2d95dc6ca25043dfad83193baa49440b not in active_clients
[2025-08-20 21:07:20.218277] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_4b68f56b65954723b071d5de0f9431d6 task=<Future at 0x281a4119450 state=finished returned Future> res=False
[2025-08-20 21:07:20.227127] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=ff3cd594fa8e46df9e899c6097498080 task=<Future at 0x281a1435600 state=finished returned Future> res=False
[2025-08-20 21:07:20.232761] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=dc5cd88b8c3e47e9bdfa5a8815f851be task=<Future at 0x281a41e2080 state=finished returned Future> res=False
[2025-08-20 21:07:20.238760] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=de23ab988bcd44e29c26c7285094d0d1 task=<Future at 0x281a4236fb0 state=finished returned Future> res=False
[2025-08-20 21:07:20.247386] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=692bbb3b3df84f7da049604fb0a375ea task=<Future at 0x281a429e440 state=finished returned Future> res=False
[2025-08-20 21:07:20.252903] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_97b4798aa7e1441b9309c86d1bc6e113 task=<Future at 0x281a4bd0250 state=finished returned Future> res=False
[2025-08-20 21:07:20.258655] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=5267cfbdfcef442b86b17519fedb70aa task=<Future at 0x281a4bc33a0 state=finished returned Future> res=False
[2025-08-20 21:07:20.265179] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_dfab6c3d9fee4095abad176324ddf9e7 task=<Future at 0x281a4c90cd0 state=finished returned Future> res=False
[2025-08-20 21:07:20.271255] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=ea62136b335444d19240495ffd1e4800 task=<Future at 0x281a4bd2ec0 state=finished returned Future> res=False
[2025-08-20 21:07:20.278698] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=f48ab274f1b34a62b95298499ba43260 task=<Future at 0x281a39ec3a0 state=finished returned Future> res=False
[2025-08-20 21:07:20.285218] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_4d2a186949014c1cbf70186befd24fc5 task=<Future at 0x281a479f1f0 state=finished returned Future> res=False
[2025-08-20 21:07:20.292771] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=e307349e6ffe4ba0a7a228f4ecd34f0d task=<Future at 0x281a48664a0 state=finished returned Future> res=False
[2025-08-20 21:07:20.297870] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=945e3686228d41b99fc7deba1fac6c4b task=<Future at 0x281a473ffa0 state=finished returned Future> res=False
[2025-08-20 21:07:20.306036] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_2d95dc6ca25043dfad83193baa49440b task=<Future at 0x281a4fb2050 state=finished returned Future> res=False
[2025-08-20 21:07:20.312586] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=dc66e06879454d938a1b3ad1a68e7cbb task=<Future at 0x281a4f2d9c0 state=finished returned Future> res=False
[2025-08-20 21:07:20.321091] [INFO process-70468-67360 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=871bff3555c44e3d9231986d1bac151b task=<Future at 0x281a4fb04f0 state=finished returned Future> res=False
[2025-08-20 21:09:16.430366] [INFO process-84904-80704 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-20 21:09:23.607380] [INFO process-84904-80704 bisheng.interface.utils:92] - trace=1 LLM caching setup with InMemoryCache
[2025-08-20 21:10:31.903832] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=bf95183a778a485884c42536f224348c GET /api/v1/web/config
[2025-08-20 21:10:33.484947] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=bf95183a778a485884c42536f224348c GET /api/v1/web/config 200 timecost=1581.115
[2025-08-20 21:10:34.896988] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=cef5003f62e74d5aac7716c39ab845cd GET /api/v1/user/info
[2025-08-20 21:10:34.906384] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=71ac9fc6e4ee478aa2fa20ba894d9db2 GET /api/v1/env
[2025-08-20 21:10:34.920189] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=339b1c016dd04e50b8d5e5dabb5d7b61 GET /api/v1/all
[2025-08-20 21:10:34.953161] [INFO process-84904-27224 bisheng.interface.custom.utils:343] - trace=339b1c016dd04e50b8d5e5dabb5d7b61 Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-20 21:10:35.200264] [INFO process-84904-27224 bisheng.interface.custom.utils:354] - trace=339b1c016dd04e50b8d5e5dabb5d7b61 Loading 1 component(s) from category custom_components
[2025-08-20 21:10:37.990076] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=cef5003f62e74d5aac7716c39ab845cd GET /api/v1/user/info 200 timecost=3093.088
[2025-08-20 21:10:38.061005] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=339b1c016dd04e50b8d5e5dabb5d7b61 GET /api/v1/all 200 timecost=3140.816
[2025-08-20 21:10:38.075571] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=93b0ed9dc33a4163a4a5482132d6ca1b GET /api/v1/component
[2025-08-20 21:10:38.090315] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=df55eb1c2667407eb112dddbd79353a4 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627
[2025-08-20 21:10:38.912240] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=df55eb1c2667407eb112dddbd79353a4 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627 200 timecost=821.925
[2025-08-20 21:10:38.956088] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=71ac9fc6e4ee478aa2fa20ba894d9db2 GET /api/v1/env 200 timecost=4049.705
[2025-08-20 21:10:39.233199] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=93b0ed9dc33a4163a4a5482132d6ca1b GET /api/v1/component 200 timecost=1157.628
[2025-08-20 21:10:39.457250] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=ec641426b5ba482a97514bfc0bd27f5b GET /api/v1/workflow/versions
[2025-08-20 21:10:39.776257] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=deada9d591904f9293f475d38207ea2c GET /api/v1/assistant/tool_list
[2025-08-20 21:10:39.788298] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=0ddc44bf28a84054bb5e04c4ec1b0875 GET /api/v1/knowledge
[2025-08-20 21:10:39.796215] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=7fa8ede13ea64275abff5ea9189004ef GET /api/v1/llm
[2025-08-20 21:10:39.813742] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=4f854bc026e64ee883aace1edcdd76b5 GET /api/v1/llm/workflow
[2025-08-20 21:10:39.822086] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=0fc8aa3b2d324fc28acc04fc676e5293 GET /api/v1/workstation/config
[2025-08-20 21:10:41.479860] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=0ddc44bf28a84054bb5e04c4ec1b0875 GET /api/v1/knowledge 200 timecost=1691.561
[2025-08-20 21:10:41.491392] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=deada9d591904f9293f475d38207ea2c GET /api/v1/assistant/tool_list 200 timecost=1715.136
[2025-08-20 21:10:42.242447] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=0fc8aa3b2d324fc28acc04fc676e5293 GET /api/v1/workstation/config 200 timecost=2420.361
[2025-08-20 21:10:42.676378] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=ec641426b5ba482a97514bfc0bd27f5b GET /api/v1/workflow/versions 200 timecost=3219.128
[2025-08-20 21:10:44.187408] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=4f854bc026e64ee883aace1edcdd76b5 GET /api/v1/llm/workflow 200 timecost=4373.666
[2025-08-20 21:10:44.588505] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=7fa8ede13ea64275abff5ea9189004ef GET /api/v1/llm 200 timecost=4792.289
[2025-08-20 21:10:45.725562] [INFO process-84904-80704 bisheng.utils.http_middleware:21] - trace=63c2926043f743d0b967f35ef3c43f65 GET /api/v1/knowledge/info
[2025-08-20 21:10:46.433966] [INFO process-84904-80704 bisheng.utils.http_middleware:24] - trace=63c2926043f743d0b967f35ef3c43f65 GET /api/v1/knowledge/info 200 timecost=708.404
[2025-08-20 21:10:46.836562] [INFO process-84904-69512 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:10:46.847494] [INFO process-84904-80704 bisheng.chat.clients.base:68] - trace=1 client_id=e25107914285425f9a1401d02b324b6c trace_id=af8468f1dc5044659ade00972619b577 message={'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id': 12, 'flow_type': 10, 'update_time': '2025-08-07T15:12:45', 'id': '0c26e44b161448ca913617e3c857d627', 'description': '', 'status': 2, 'guide_word': None, 'create_time': '2025-07-31T15:46:27', 'nodes': [{'id': 'start_25626', 'data': {'v': '1', 'id': 'start_25626', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': [''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': '1afc3c', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 614}, 'position': {'x': 720, 'y': 20}, 'selected': False}, {'id': 'input_1fb3a', 'data': {'v': '2', 'id': 'input_1fb3a', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '输入文本内容', 'global': 'key'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 420}, 'position': {'x': 1155, 'y': 15}, 'selected': False}, {'id': 'rag_63d89', 'data': {'v': '1', 'id': 'rag_63d89', 'name': '文档知识库问答', 'type': 'rag', 'description': '根据用户问题从知识库中检索相关内容，结合检索结果调用大模型生成最终结果，支持多个问题并行执行。', 'group_params': [{'name': '知识库检索设置', 'params': [{'key': 'user_question', 'help': '当选择多个问题时，将会多次运行本节点，每次运行时从批量问题中取一项进行处理。', 'test': 'var', 'type': 'user_question', 'label': '用户问题', 'value': ['input_1fb3a.user_input'], 'varZh': {'input_1fb3a.user_input': '输入/user_input'}, 'global': 'self=user_prompt', 'linkage': 'output_user_input', 'required': True, 'placeholder': '请选择用户问题'}, {'key': 'knowledge', 'type': 'knowledge_select_multi', 'label': '检索范围', 'value': {'type': 'knowledge', 'value': [{'key': 124, 'label': '比亚迪-秦PLUS-车型知识库'}]}, 'required': True, 'placeholder': '请选择知识库'}, {'key': 'user_auth', 'help': '开启后，只会对用户有使用权限的知识库进行检索。', 'type': 'switch', 'label': '用户知识库权限校验', 'value': False}, {'key': 'max_chunk_size', 'help': '通过此参数控制最终传给模型的知识库检索结果文本长度，超过模型支持的最大上下文长度可能会导致报错。', 'type': 'number', 'label': '检索结果长度', 'value': 15000}, {'key': 'retrieved_result', 'type': 'var', 'label': '检索结果', 'global': 'self=user_prompt'}]}, {'name': 'AI回复生成设置', 'params': [{'key': 'system_prompt', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'required': True}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '用户问题：```{{#rag_63d89.user_question#}}```\n参考文本：```{{#rag_63d89.retrieved_result#}}```\n你的回答：', 'varZh': {'rag_63d89.user_question': 'user_question', 'rag_63d89.retrieved_result': 'retrieved_result'}, 'required': True}, {'key': 'model_id', 'type': 'bisheng_model', 'label': '模型', 'value': 82, 'required': True, 'placeholder': '请在模型管理中配置 LLM 模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output_user_input', 'help': '模型输出内容将会存储在该变量中。', 'type': 'var', 'label': '输出变量', 'value': [{'key': 'output_user_input', 'label': 'output_user_input'}], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1097}, 'position': {'x': 1575, 'y': 15}, 'selected': True}], 'edges': [{'id': 'xy-edge__input_1fb3aright_handle-rag_63d89left_handle', 'type': 'customEdge', 'source': 'input_1fb3a', 'target': 'rag_63d89', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__start_25626right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'start_25626', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__rag_63d89right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'rag_63d89', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': -86.75296262534175, 'y': 25.15496809480402, 'zoom': 0.6563354603463992}}}
[2025-08-20 21:10:47.023595] [INFO process-84904-80952 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:10:47.891394] [INFO process-84904-69512 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-20 21:10:48.046205] [INFO process-84904-80952 bisheng.utils.threadpool:67] - trace=af8468f1dc5044659ade00972619b577 async_task_added fun=wrapper_task args=af8468f1dc5044659ade00972619b577
[2025-08-20 21:11:08.925460] [INFO process-84904-80704 bisheng.chat.clients.base:68] - trace=1 client_id=e25107914285425f9a1401d02b324b6c trace_id=1b2eb63e7e0c4c5c81301e00574ed544 message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': '你好，你知道比亚迪吗？', 'dialog_files_content': []}, 'message': '你好，你知道比亚迪吗？', 'message_id': 'd81b83c19289410c9a3424a1794417a1', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 21:11:08.953231] [INFO process-84904-69512 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:11:08.978430] [INFO process-84904-84368 bisheng.chat.clients.workflow_client:182] - trace=1b2eb63e7e0c4c5c81301e00574ed544 get user input: {'input_1fb3a': {'data': {'user_input': '你好，你知道比亚迪吗？', 'dialog_files_content': []}, 'message': '你好，你知道比亚迪吗？', 'message_id': 'd81b83c19289410c9a3424a1794417a1', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 21:11:09.968462] [INFO process-84904-69512 bisheng.utils.threadpool:67] - trace=1b2eb63e7e0c4c5c81301e00574ed544 async_task_added fun=wrapper_task args=1b2eb63e7e0c4c5c81301e00574ed544
[2025-08-20 21:12:27.744322] [INFO process-84904-80704 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1012, None)
[2025-08-20 21:12:27.751137] [INFO process-84904-80704 bisheng.chat.manager:181] - trace=1 close_client client_key=e25107914285425f9a1401d02b324b6c
[2025-08-20 21:12:28.221782] [ERROR process-84904-76528 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 12, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "e25107914285425f9a1401d02b324b6c"}', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'chat_id': ''} error: Unexpected ASGI message 'websocket.send', after sending 'websocket.close'.
[2025-08-20 21:12:28.309559] [ERROR process-84904-80704 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-20 21:12:28.314981] [WARNING process-84904-80704 bisheng.chat.manager:179] - trace=1 close_client client_key=e25107914285425f9a1401d02b324b6c not in active_clients
[2025-08-20 21:12:28.321061] [INFO process-84904-80704 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_e25107914285425f9a1401d02b324b6c task=<Future at 0x247fe2d2a70 state=finished returned Future> res=False
[2025-08-20 21:12:28.329921] [INFO process-84904-80704 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=af8468f1dc5044659ade00972619b577 task=<Future at 0x247fe2d3910 state=finished returned Future> res=False
[2025-08-20 21:13:20.386629] [INFO process-71428-78936 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-20 21:13:28.755155] [INFO process-71428-78936 bisheng.interface.utils:92] - trace=1 LLM caching setup with InMemoryCache
[2025-08-20 21:13:50.750321] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=1f5297858ef042eeb055a9a4ab971daf GET /api/v1/web/config
[2025-08-20 21:13:51.496116] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=1f5297858ef042eeb055a9a4ab971daf GET /api/v1/web/config 200 timecost=745.795
[2025-08-20 21:13:53.987045] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=bc4c868b29b744b79a1240f7f984c284 GET /api/v1/user/info
[2025-08-20 21:13:53.993631] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=be87deb14b7b48149ec906707806e356 GET /api/v1/env
[2025-08-20 21:13:54.003364] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=9bb6627812c54b8c8a68bdb126067a39 GET /api/v1/all
[2025-08-20 21:13:54.017670] [INFO process-71428-80948 bisheng.interface.custom.utils:343] - trace=9bb6627812c54b8c8a68bdb126067a39 Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-20 21:13:54.134985] [INFO process-71428-80948 bisheng.interface.custom.utils:354] - trace=9bb6627812c54b8c8a68bdb126067a39 Loading 1 component(s) from category custom_components
[2025-08-20 21:13:56.696112] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=bc4c868b29b744b79a1240f7f984c284 GET /api/v1/user/info 200 timecost=2709.068
[2025-08-20 21:13:56.701876] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=9bb6627812c54b8c8a68bdb126067a39 GET /api/v1/all 200 timecost=2698.512
[2025-08-20 21:13:56.709124] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=be87deb14b7b48149ec906707806e356 GET /api/v1/env 200 timecost=2715.494
[2025-08-20 21:13:56.720162] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=fe32a45166ce4e07b2a700b2485be9a8 GET /api/v1/component
[2025-08-20 21:13:56.729365] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=eea08994af3547429f6c073ad3c66265 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627
[2025-08-20 21:13:56.739383] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=3b7f8651472c4783925aabff0a640fe7 GET /api/v1/workstation/config
[2025-08-20 21:13:57.103355] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=fe32a45166ce4e07b2a700b2485be9a8 GET /api/v1/component 200 timecost=384.274
[2025-08-20 21:13:57.510944] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=eea08994af3547429f6c073ad3c66265 GET /api/v1/flows/0c26e44b161448ca913617e3c857d627 200 timecost=781.579
[2025-08-20 21:13:57.979723] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=3b7f8651472c4783925aabff0a640fe7 GET /api/v1/workstation/config 200 timecost=1240.339
[2025-08-20 21:13:58.321675] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=c77fb16f94e749b6a20cd1365f793852 GET /api/v1/workflow/versions
[2025-08-20 21:13:58.326938] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=552f1851237e43479eaed551e5d26648 GET /api/v1/assistant/tool_list
[2025-08-20 21:13:58.333815] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=d3a5abb79a4a4667830966ec84d39c5b GET /api/v1/knowledge
[2025-08-20 21:13:58.339331] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=e59227635aba40a7919dec9120d7158f GET /api/v1/llm
[2025-08-20 21:13:58.354108] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=73c633d9964a41159ec209163f5e0fcb GET /api/v1/llm/workflow
[2025-08-20 21:13:59.134946] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=c77fb16f94e749b6a20cd1365f793852 GET /api/v1/workflow/versions 200 timecost=813.272
[2025-08-20 21:14:00.178284] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=d3a5abb79a4a4667830966ec84d39c5b GET /api/v1/knowledge 200 timecost=1844.469
[2025-08-20 21:14:00.184415] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=e59227635aba40a7919dec9120d7158f GET /api/v1/llm 200 timecost=1845.084
[2025-08-20 21:14:00.191149] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=552f1851237e43479eaed551e5d26648 GET /api/v1/assistant/tool_list 200 timecost=1864.211
[2025-08-20 21:14:00.199177] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=614c9d395fbb48d4a5a438020ebb7699 GET /api/v1/knowledge/info
[2025-08-20 21:14:00.503358] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=73c633d9964a41159ec209163f5e0fcb GET /api/v1/llm/workflow 200 timecost=2150.246
[2025-08-20 21:14:00.576548] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=614c9d395fbb48d4a5a438020ebb7699 GET /api/v1/knowledge/info 200 timecost=377.371
[2025-08-20 21:14:00.946363] [INFO process-71428-7364 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:14:00.951475] [INFO process-71428-78936 bisheng.chat.clients.base:68] - trace=1 client_id=e0e513e496ec492c8d9603f334c66483 trace_id=******************************** message={'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id': 12, 'flow_type': 10, 'update_time': '2025-08-07T15:12:45', 'id': '0c26e44b161448ca913617e3c857d627', 'description': '', 'status': 2, 'guide_word': None, 'create_time': '2025-07-31T15:46:27', 'nodes': [{'id': 'start_25626', 'data': {'v': '1', 'id': 'start_25626', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': [''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': '1afc3c', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 614}, 'position': {'x': 720, 'y': 20}, 'selected': False}, {'id': 'input_1fb3a', 'data': {'v': '2', 'id': 'input_1fb3a', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '输入文本内容', 'global': 'key'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 420}, 'position': {'x': 1155, 'y': 15}, 'selected': False}, {'id': 'rag_63d89', 'data': {'v': '1', 'id': 'rag_63d89', 'name': '文档知识库问答', 'type': 'rag', 'description': '根据用户问题从知识库中检索相关内容，结合检索结果调用大模型生成最终结果，支持多个问题并行执行。', 'group_params': [{'name': '知识库检索设置', 'params': [{'key': 'user_question', 'help': '当选择多个问题时，将会多次运行本节点，每次运行时从批量问题中取一项进行处理。', 'test': 'var', 'type': 'user_question', 'label': '用户问题', 'value': ['input_1fb3a.user_input'], 'varZh': {'input_1fb3a.user_input': '输入/user_input'}, 'global': 'self=user_prompt', 'linkage': 'output_user_input', 'required': True, 'placeholder': '请选择用户问题'}, {'key': 'knowledge', 'type': 'knowledge_select_multi', 'label': '检索范围', 'value': {'type': 'knowledge', 'value': [{'key': 124, 'label': '比亚迪-秦PLUS-车型知识库'}]}, 'required': True, 'placeholder': '请选择知识库'}, {'key': 'user_auth', 'help': '开启后，只会对用户有使用权限的知识库进行检索。', 'type': 'switch', 'label': '用户知识库权限校验', 'value': False}, {'key': 'max_chunk_size', 'help': '通过此参数控制最终传给模型的知识库检索结果文本长度，超过模型支持的最大上下文长度可能会导致报错。', 'type': 'number', 'label': '检索结果长度', 'value': 15000}, {'key': 'retrieved_result', 'type': 'var', 'label': '检索结果', 'global': 'self=user_prompt'}]}, {'name': 'AI回复生成设置', 'params': [{'key': 'system_prompt', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'required': True}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '用户问题：```{{#rag_63d89.user_question#}}```\n参考文本：```{{#rag_63d89.retrieved_result#}}```\n你的回答：', 'varZh': {'rag_63d89.user_question': 'user_question', 'rag_63d89.retrieved_result': 'retrieved_result'}, 'required': True}, {'key': 'model_id', 'type': 'bisheng_model', 'label': '模型', 'value': 82, 'required': True, 'placeholder': '请在模型管理中配置 LLM 模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output_user_input', 'help': '模型输出内容将会存储在该变量中。', 'type': 'var', 'label': '输出变量', 'value': [{'key': 'output_user_input', 'label': 'output_user_input'}], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1097}, 'position': {'x': 1575, 'y': 15}, 'selected': True}], 'edges': [{'id': 'xy-edge__input_1fb3aright_handle-rag_63d89left_handle', 'type': 'customEdge', 'source': 'input_1fb3a', 'target': 'rag_63d89', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__start_25626right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'start_25626', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__rag_63d89right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'rag_63d89', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': -86.75296262534175, 'y': 25.15496809480402, 'zoom': 0.6563354603463992}}}
[2025-08-20 21:14:01.045420] [INFO process-71428-82736 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:14:02.013077] [INFO process-71428-7364 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-20 21:14:02.058422] [INFO process-71428-82736 bisheng.utils.threadpool:67] - trace=******************************** async_task_added fun=wrapper_task args=********************************
[2025-08-20 21:14:12.999081] [INFO process-71428-78936 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1005, None)
[2025-08-20 21:14:13.005634] [INFO process-71428-78936 bisheng.chat.manager:181] - trace=1 close_client client_key=e0e513e496ec492c8d9603f334c66483
[2025-08-20 21:14:13.195897] [ERROR process-71428-84544 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': True, 'message': {'unique_id': '6173f31d2fd648639252c92b6ccb6344', 'node_id': 'start_25626', 'name': '开始'}, 'type': 'start', 'category': 'node_run', 'intermediate_steps': '', 'files': [], 'user_id': None, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{}', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'chat_id': ''} error: Unexpected ASGI message 'websocket.send', after sending 'websocket.close'.
[2025-08-20 21:14:13.437252] [ERROR process-71428-78936 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-20 21:14:13.444415] [WARNING process-71428-78936 bisheng.chat.manager:179] - trace=1 close_client client_key=e0e513e496ec492c8d9603f334c66483 not in active_clients
[2025-08-20 21:14:13.458680] [ERROR process-71428-21540 bisheng.chat.clients.workflow_client:146] - trace=******************************** init_workflow_error
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_bundle\pydev_monkey.py", line 817, in __call__
    ret = self.original_func(*self.args, **self.kwargs)
          │    │              │    │       │    └ {}
          │    │              │    │       └ <_pydev_bundle.pydev_monkey._NewThreadStartupWithTrace object at 0x0000022F8D9D94B0>
          │    │              │    └ ()
          │    │              └ <_pydev_bundle.pydev_monkey._NewThreadStartupWithTrace object at 0x0000022F8D9D94B0>
          │    └ <bound method Thread._bootstrap of <Thread(Thread-7 (start_loop), started 21540)>>
          └ <_pydev_bundle.pydev_monkey._NewThreadStartupWithTrace object at 0x0000022F8D9D94B0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 966, in _bootstrap
    self._bootstrap_inner()
    │    └ <function Thread._bootstrap_inner at 0x0000022FAD9784C0>
    └ <Thread(Thread-7 (start_loop), started 21540)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1009, in _bootstrap_inner
    self.run()
    │    └ <function Thread.run at 0x0000022FAD9781F0>
    └ <Thread(Thread-7 (start_loop), started 21540)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 946, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {}
    │    │        │    │        └ <Thread(Thread-7 (start_loop), started 21540)>
    │    │        │    └ (<ProactorEventLoop running=True closed=False debug=False>,)
    │    │        └ <Thread(Thread-7 (start_loop), started 21540)>
    │    └ <bound method ThreadPoolManager.start_loop of <bisheng.utils.threadpool.ThreadPoolManager object at 0x0000022F858B5360>>
    └ <Thread(Thread-7 (start_loop), started 21540)>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\utils\threadpool.py", line 72, in start_loop
    loop.run_forever()
    │    └ <function _patch_loop.<locals>.run_forever at 0x0000022FC634E0E0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 218, in run_forever
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x0000022FC634E200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000022FC620FA30>
    └ <Handle Task.__wakeup(<Future finished result=None>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.__wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.__wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.__wakeup(<Future finished result=None>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 315, in __wakeup
    self.__step()
    └ <Task pending name='Task-58' coro=<BaseClient.wrapper_task() running at D:\projects\postwise-v2.0\postwise\src\backend\bishen...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 395, in step
    step_orig(task, exc)
    │         │     └ None
    │         └ <Task pending name='Task-58' coro=<BaseClient.wrapper_task() running at D:\projects\postwise-v2.0\postwise\src\backend\bishen...
    └ <function Task.__step at 0x0000022FC6256F80>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 232, in __step
    result = coro.send(None)
             │    └ <method 'send' of 'coroutine' objects>
             └ <coroutine object BaseClient.wrapper_task at 0x0000022F8D7B3A70>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\chat\clients\base.py", line 90, in wrapper_task
    await fn(*args, **kwargs)
          │   │       └ {}
          │   └ ({'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id'...
          └ <bound method WorkflowClient._handle_message of <bisheng.chat.clients.workflow_client.WorkflowClient object at 0x0000022F8D46...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\chat\clients\workflow_client.py", line 55, in _handle_message
    await self.init_workflow(message)
          │    │             └ {'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id':...
          │    └ <function WorkflowClient.init_workflow at 0x0000022F866255A0>
          └ <bisheng.chat.clients.workflow_client.WorkflowClient object at 0x0000022F8D46DB10>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\chat\clients\workflow_client.py", line 144, in init_workflow
    await self.workflow_run()
          │    └ <function WorkflowClient.workflow_run at 0x0000022F86625630>
          └ <bisheng.chat.clients.workflow_client.WorkflowClient object at 0x0000022F8D46DB10>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\chat\clients\workflow_client.py", line 152, in workflow_run
    await self._workflow_run()
          │    └ <function WorkflowClient._workflow_run at 0x0000022F866256C0>
          └ <bisheng.chat.clients.workflow_client.WorkflowClient object at 0x0000022F8D46DB10>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\chat\clients\workflow_client.py", line 172, in _workflow_run
    self.workflow.clear_workflow_status()
    │    └ None
    └ <bisheng.chat.clients.workflow_client.WorkflowClient object at 0x0000022F8D46DB10>

AttributeError: 'NoneType' object has no attribute 'clear_workflow_status'
[2025-08-20 21:14:21.901885] [INFO process-71428-78936 bisheng.utils.http_middleware:21] - trace=b86ad441f30045eea8fd639348aad44c GET /api/v1/knowledge/info
[2025-08-20 21:14:22.518094] [INFO process-71428-78936 bisheng.utils.http_middleware:24] - trace=b86ad441f30045eea8fd639348aad44c GET /api/v1/knowledge/info 200 timecost=616.209
[2025-08-20 21:14:22.863570] [INFO process-71428-78936 bisheng.chat.clients.base:68] - trace=1 client_id=b1d8987193e444b8b3817ca4eb3d3063 trace_id=******************************** message={'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id': 12, 'flow_type': 10, 'update_time': '2025-08-07T15:12:45', 'id': '0c26e44b161448ca913617e3c857d627', 'description': '', 'status': 2, 'guide_word': None, 'create_time': '2025-07-31T15:46:27', 'nodes': [{'id': 'start_25626', 'data': {'v': '1', 'id': 'start_25626', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': [''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': '1afc3c', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 614}, 'position': {'x': 720, 'y': 20}, 'selected': False}, {'id': 'input_1fb3a', 'data': {'v': '2', 'id': 'input_1fb3a', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '输入文本内容', 'global': 'key'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 420}, 'position': {'x': 1155, 'y': 15}, 'selected': False}, {'id': 'rag_63d89', 'data': {'v': '1', 'id': 'rag_63d89', 'name': '文档知识库问答', 'type': 'rag', 'description': '根据用户问题从知识库中检索相关内容，结合检索结果调用大模型生成最终结果，支持多个问题并行执行。', 'group_params': [{'name': '知识库检索设置', 'params': [{'key': 'user_question', 'help': '当选择多个问题时，将会多次运行本节点，每次运行时从批量问题中取一项进行处理。', 'test': 'var', 'type': 'user_question', 'label': '用户问题', 'value': ['input_1fb3a.user_input'], 'varZh': {'input_1fb3a.user_input': '输入/user_input'}, 'global': 'self=user_prompt', 'linkage': 'output_user_input', 'required': True, 'placeholder': '请选择用户问题'}, {'key': 'knowledge', 'type': 'knowledge_select_multi', 'label': '检索范围', 'value': {'type': 'knowledge', 'value': [{'key': 124, 'label': '比亚迪-秦PLUS-车型知识库'}]}, 'required': True, 'placeholder': '请选择知识库'}, {'key': 'user_auth', 'help': '开启后，只会对用户有使用权限的知识库进行检索。', 'type': 'switch', 'label': '用户知识库权限校验', 'value': False}, {'key': 'max_chunk_size', 'help': '通过此参数控制最终传给模型的知识库检索结果文本长度，超过模型支持的最大上下文长度可能会导致报错。', 'type': 'number', 'label': '检索结果长度', 'value': 15000}, {'key': 'retrieved_result', 'type': 'var', 'label': '检索结果', 'global': 'self=user_prompt'}]}, {'name': 'AI回复生成设置', 'params': [{'key': 'system_prompt', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'required': True}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '用户问题：```{{#rag_63d89.user_question#}}```\n参考文本：```{{#rag_63d89.retrieved_result#}}```\n你的回答：', 'varZh': {'rag_63d89.user_question': 'user_question', 'rag_63d89.retrieved_result': 'retrieved_result'}, 'required': True}, {'key': 'model_id', 'type': 'bisheng_model', 'label': '模型', 'value': 82, 'required': True, 'placeholder': '请在模型管理中配置 LLM 模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output_user_input', 'help': '模型输出内容将会存储在该变量中。', 'type': 'var', 'label': '输出变量', 'value': [{'key': 'output_user_input', 'label': 'output_user_input'}], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1097}, 'position': {'x': 1575, 'y': 15}, 'selected': True}], 'edges': [{'id': 'xy-edge__input_1fb3aright_handle-rag_63d89left_handle', 'type': 'customEdge', 'source': 'input_1fb3a', 'target': 'rag_63d89', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__start_25626right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'start_25626', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__rag_63d89right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'rag_63d89', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': -86.75296262534175, 'y': 25.15496809480402, 'zoom': 0.6563354603463992}}}
[2025-08-20 21:14:22.929061] [INFO process-71428-7364 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:14:22.959391] [INFO process-71428-36044 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:14:23.970984] [INFO process-71428-7364 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-20 21:14:23.974453] [INFO process-71428-36044 bisheng.utils.threadpool:67] - trace=******************************** async_task_added fun=wrapper_task args=********************************
[2025-08-20 21:14:28.297622] [INFO process-71428-78936 bisheng.chat.clients.base:68] - trace=1 client_id=b1d8987193e444b8b3817ca4eb3d3063 trace_id=26653f0ba0cf4ea49db4ac4a6608853e message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': '你好吗？', 'dialog_files_content': []}, 'message': '你好吗？', 'message_id': '7ae17e83e7ce4052ba1f2380ed82d58f', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 21:14:28.321602] [INFO process-71428-82736 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:14:28.346582] [INFO process-71428-75832 bisheng.chat.clients.workflow_client:182] - trace=26653f0ba0cf4ea49db4ac4a6608853e get user input: {'input_1fb3a': {'data': {'user_input': '你好吗？', 'dialog_files_content': []}, 'message': '你好吗？', 'message_id': '7ae17e83e7ce4052ba1f2380ed82d58f', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 21:14:29.344016] [INFO process-71428-82736 bisheng.utils.threadpool:67] - trace=26653f0ba0cf4ea49db4ac4a6608853e async_task_added fun=wrapper_task args=26653f0ba0cf4ea49db4ac4a6608853e
[2025-08-20 21:15:05.067900] [INFO process-71428-78936 bisheng.chat.clients.base:68] - trace=1 client_id=b1d8987193e444b8b3817ca4eb3d3063 trace_id=357cf15cdbf04ab2a5a5a128e66fc188 message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': 'i加强为哦机器卫栖梧', 'dialog_files_content': []}, 'message': 'i加强为哦机器卫栖梧', 'message_id': 'c1711b086e7b4101bb889cf659372f5a', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 21:15:05.093059] [INFO process-71428-7364 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:15:05.110860] [INFO process-71428-79608 bisheng.chat.clients.workflow_client:182] - trace=357cf15cdbf04ab2a5a5a128e66fc188 get user input: {'input_1fb3a': {'data': {'user_input': 'i加强为哦机器卫栖梧', 'dialog_files_content': []}, 'message': 'i加强为哦机器卫栖梧', 'message_id': 'c1711b086e7b4101bb889cf659372f5a', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 21:15:06.108231] [INFO process-71428-7364 bisheng.utils.threadpool:67] - trace=357cf15cdbf04ab2a5a5a128e66fc188 async_task_added fun=wrapper_task args=357cf15cdbf04ab2a5a5a128e66fc188
[2025-08-20 21:16:22.907349] [ERROR process-71428-78936 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...shed result=6>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...shed result=6>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x0000022FC630B010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 61442, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x0000022FC630A050>
              └ <__main__.PyDB object at 0x0000022FC62E5570>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x0000022FC630A0E0>
           └ <__main__.PyDB object at 0x0000022FC62E5570>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x0000022FC5CEEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x0000022F8B004460>
    │       └ <function run at 0x0000022F856E0E50>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000022F856E08B0>
    └ <uvicorn.server.Server object at 0x0000022F8D44C790>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000022F856E0940>
           │       │   └ <uvicorn.server.Server object at 0x0000022F8D44C790>
           │       └ <function _patch_asyncio.<locals>.run at 0x0000022FC634D6C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x0000022FC634E170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x0000022FC634E200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000022FC620FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...shed result=6>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...shed result=6>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...shed result=6>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...shed result=6>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=4584 read=<_OverlappedFuture finished result=10> write=<_OverlappedFuture finished result=10> wr...
           └ <_OverlappedFuture finished result=6>

AssertionError: assert f is self._write_fut
[2025-08-20 21:24:47.555626] [INFO process-71428-78936 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1012, None)
[2025-08-20 21:24:47.560636] [INFO process-71428-78936 bisheng.chat.manager:181] - trace=1 close_client client_key=b1d8987193e444b8b3817ca4eb3d3063
[2025-08-20 21:24:47.713788] [ERROR process-71428-78936 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-20 21:24:47.716781] [WARNING process-71428-78936 bisheng.chat.manager:179] - trace=1 close_client client_key=b1d8987193e444b8b3817ca4eb3d3063 not in active_clients
[2025-08-20 21:24:47.722755] [INFO process-71428-78936 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_e0e513e496ec492c8d9603f334c66483 task=<Future at 0x22f8d858580 state=finished returned Future> res=False
[2025-08-20 21:24:47.728179] [INFO process-71428-78936 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=******************************** task=<Future at 0x22f8da4c970 state=finished returned Future> res=False
[2025-08-20 21:24:47.732300] [INFO process-71428-78936 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_b1d8987193e444b8b3817ca4eb3d3063 task=<Future at 0x22f8db17b80 state=finished returned Future> res=True
[2025-08-20 21:24:47.737366] [INFO process-71428-78936 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=******************************** task=<Future at 0x22f8db17d30 state=finished returned Future> res=False
[2025-08-20 21:24:47.742265] [INFO process-71428-78936 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=26653f0ba0cf4ea49db4ac4a6608853e task=<Future at 0x22f8dc2ad40 state=finished returned Future> res=False
[2025-08-20 21:24:47.745928] [INFO process-71428-78936 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=357cf15cdbf04ab2a5a5a128e66fc188 task=<Future at 0x22f8dc17df0 state=finished returned Future> res=False
[2025-08-20 21:25:31.552687] [INFO process-81064-69172 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-20 21:25:35.497100] [INFO process-81064-69172 bisheng.interface.utils:92] - trace=1 LLM caching setup with InMemoryCache
[2025-08-20 21:25:53.696077] [INFO process-81064-69172 bisheng.utils.http_middleware:21] - trace=26caea9bae8e4423bbc63cd1c1050ddb GET /api/v1/knowledge/info
[2025-08-20 21:25:55.987980] [INFO process-81064-69172 bisheng.utils.http_middleware:24] - trace=26caea9bae8e4423bbc63cd1c1050ddb GET /api/v1/knowledge/info 200 timecost=2291.903
[2025-08-20 21:25:56.347231] [INFO process-81064-62364 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:25:56.354727] [INFO process-81064-69172 bisheng.chat.clients.base:68] - trace=1 client_id=841181918b17447aaae0fa202921f3f2 trace_id=953b56589a57417ebdf2a3b8c94cfb0b message={'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id': 12, 'flow_type': 10, 'update_time': '2025-08-07T15:12:45', 'id': '0c26e44b161448ca913617e3c857d627', 'description': '', 'status': 2, 'guide_word': None, 'create_time': '2025-07-31T15:46:27', 'nodes': [{'id': 'start_25626', 'data': {'v': '1', 'id': 'start_25626', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': [''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': '1afc3c', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 614}, 'position': {'x': 720, 'y': 20}, 'selected': False}, {'id': 'input_1fb3a', 'data': {'v': '2', 'id': 'input_1fb3a', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '输入文本内容', 'global': 'key'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 420}, 'position': {'x': 1155, 'y': 15}, 'selected': False}, {'id': 'rag_63d89', 'data': {'v': '1', 'id': 'rag_63d89', 'name': '文档知识库问答', 'type': 'rag', 'description': '根据用户问题从知识库中检索相关内容，结合检索结果调用大模型生成最终结果，支持多个问题并行执行。', 'group_params': [{'name': '知识库检索设置', 'params': [{'key': 'user_question', 'help': '当选择多个问题时，将会多次运行本节点，每次运行时从批量问题中取一项进行处理。', 'test': 'var', 'type': 'user_question', 'label': '用户问题', 'value': ['input_1fb3a.user_input'], 'varZh': {'input_1fb3a.user_input': '输入/user_input'}, 'global': 'self=user_prompt', 'linkage': 'output_user_input', 'required': True, 'placeholder': '请选择用户问题'}, {'key': 'knowledge', 'type': 'knowledge_select_multi', 'label': '检索范围', 'value': {'type': 'knowledge', 'value': [{'key': 124, 'label': '比亚迪-秦PLUS-车型知识库'}]}, 'required': True, 'placeholder': '请选择知识库'}, {'key': 'user_auth', 'help': '开启后，只会对用户有使用权限的知识库进行检索。', 'type': 'switch', 'label': '用户知识库权限校验', 'value': False}, {'key': 'max_chunk_size', 'help': '通过此参数控制最终传给模型的知识库检索结果文本长度，超过模型支持的最大上下文长度可能会导致报错。', 'type': 'number', 'label': '检索结果长度', 'value': 15000}, {'key': 'retrieved_result', 'type': 'var', 'label': '检索结果', 'global': 'self=user_prompt'}]}, {'name': 'AI回复生成设置', 'params': [{'key': 'system_prompt', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'required': True}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '用户问题：```{{#rag_63d89.user_question#}}```\n参考文本：```{{#rag_63d89.retrieved_result#}}```\n你的回答：', 'varZh': {'rag_63d89.user_question': 'user_question', 'rag_63d89.retrieved_result': 'retrieved_result'}, 'required': True}, {'key': 'model_id', 'type': 'bisheng_model', 'label': '模型', 'value': 82, 'required': True, 'placeholder': '请在模型管理中配置 LLM 模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output_user_input', 'help': '模型输出内容将会存储在该变量中。', 'type': 'var', 'label': '输出变量', 'value': [{'key': 'output_user_input', 'label': 'output_user_input'}], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1097}, 'position': {'x': 1575, 'y': 15}, 'selected': True}], 'edges': [{'id': 'xy-edge__input_1fb3aright_handle-rag_63d89left_handle', 'type': 'customEdge', 'source': 'input_1fb3a', 'target': 'rag_63d89', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__start_25626right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'start_25626', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__rag_63d89right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'rag_63d89', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': -86.75296262534175, 'y': 25.15496809480402, 'zoom': 0.6563354603463992}}}
[2025-08-20 21:25:56.444603] [INFO process-81064-31420 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:25:57.380386] [INFO process-81064-62364 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-20 21:25:57.458390] [INFO process-81064-31420 bisheng.utils.threadpool:67] - trace=953b56589a57417ebdf2a3b8c94cfb0b async_task_added fun=wrapper_task args=953b56589a57417ebdf2a3b8c94cfb0b
[2025-08-20 21:26:15.215177] [INFO process-81064-69172 bisheng.chat.clients.base:68] - trace=1 client_id=841181918b17447aaae0fa202921f3f2 trace_id=25b81746967d48bd8e823c439ca2912a message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': '你好，请问你知道什么车型？尽量详细点。', 'dialog_files_content': []}, 'message': '你好，请问你知道什么车型？尽量详细点。', 'message_id': 'dbea5e9732ea4f8fba1d65a6098398b2', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 21:26:15.243658] [INFO process-81064-62364 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:26:15.266332] [INFO process-81064-66652 bisheng.chat.clients.workflow_client:182] - trace=25b81746967d48bd8e823c439ca2912a get user input: {'input_1fb3a': {'data': {'user_input': '你好，请问你知道什么车型？尽量详细点。', 'dialog_files_content': []}, 'message': '你好，请问你知道什么车型？尽量详细点。', 'message_id': 'dbea5e9732ea4f8fba1d65a6098398b2', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 21:26:16.266964] [INFO process-81064-62364 bisheng.utils.threadpool:67] - trace=25b81746967d48bd8e823c439ca2912a async_task_added fun=wrapper_task args=25b81746967d48bd8e823c439ca2912a
[2025-08-20 21:28:22.089620] [INFO process-81064-69172 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1005, None)
[2025-08-20 21:28:22.097144] [INFO process-81064-69172 bisheng.chat.manager:181] - trace=1 close_client client_key=841181918b17447aaae0fa202921f3f2
[2025-08-20 21:28:22.298925] [ERROR process-81064-63968 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': True, 'message': {'unique_id': '69283a06213a4c21b60e29378dea786a', 'node_id': 'rag_63d89', 'name': '文档知识库问答', 'reason': None, 'log_data': [[{'key': 'rag_63d89.user_question', 'value': '你好，请问你知道什么车型？尽量详细点。', 'type': 'variable'}, {'key': 'rag_63d89.retrieved_result', 'value': '[\n  "{<file_title>政策宣讲.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是比亚迪秦PLUS智驾版车型的限时促销政策宣讲文档，重点介绍EV/DM-i双版本的三重补贴政策、旧车置换优惠及车型性能优势。核心内容包括6.98万起售的补贴价、最高1万元综合补贴以及全系开放置换资格，活动截止至4月30日。\\",\\n  \\"KeyInfo\\": [\\n    \\"三重补贴最高1万元（厂家6000元+地方4000元）\\",\\n    \\"旧车置换全系可享（含DM-i入门款）\\",\\n    \\"DM-i百公里油耗2.93L/EV续航510km\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>政策宣讲：\\n\\n现在想买我们秦 PLUS 的老板们，你们真的是赶上好时候了！第二代秦 PLUS 智驾版双王炸限时钜惠！EV 版限时综合补贴价 9.98 万起，DM 版更是 6.98 万起！三重重磅补贴直接给到您！第一弹：厂家补贴直接加码，升级到 6000 元！比亚迪想跟您交个朋友的真心真是有目共睹啊。第二弹：至少 4000 元的地方补贴！想知道您在的城市具体是什么价格？可以后台联系我们小助手，也可以直接小风车预约一些； 第三弹：以前 DM-i 入门款不能参与以旧换新大家觉得很可惜，那今天直接给您放开了！第二代秦 PLUS DM-i 智驾版入门款也可以参与置换补贴！以前只有 EV 版入门款有置换资格，现在全系开放，0 压力升级智驾黑科技！ 敲重点，上述三重补贴 DM-i 版、EV 版都可享受！\\n\\n引导留资：</paragraph_content>}",\n  "{<file_title>内饰.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是汽车内饰设计说明，详细介绍了第二代秦PLUS EV智驾版的内饰特点。文档通过环抱式座舱、中国元素融入及科技感材质等设计亮点，突出其舒适性与科技美学的结合，同时强调空间实用性。\\",\\n  \\"KeyInfo\\": [\\n    \\"环抱式阔境悬浮座舱营造沉浸式驾乘体验\\",\\n    \\"中控台与方向盘融合中国结等传统元素与科技设计\\",\\n    \\"提供暮云灰/砂金米配色及全平中央通道空间优化\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>最后再聊聊空间，这款车的空间真的是宽绰得没话说。后排采用了全平中央通道设计家里有老人小孩的都知道，以前坐后排中间，要是有个高高的通道凸起，脚都没地儿放，特别难受。但在咱们这款车上就完全不用担心，就算坐在中间位置，也能像坐在两边一样舒适还有后备箱空间，日常家庭采购、周末去超市囤货，完全不在话下。要是来一场自驾游，把后排座椅 4/6 比例放倒，能装下好多行李，一点压力都没有。</paragraph_content>}",\n  "{<file_title>智驾.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是产品功能说明，详细介绍第二代秦 PLUS DM-i 智驾版的高阶智能驾驶系统。文档重点介绍了29个传感器配置、高快领航与全场景泊车功能，并强调其以6.98万起的亲民价格打破高端智驾技术门槛。\\",\\n  \\"KeyInfo\\": [\\n    \\"搭载29个传感器的天神之眼C三目版系统\\",\\n    \\"高快领航与全场景智能泊车功能覆盖复杂场景\\",\\n    \\"6.98万起售价打破高阶智驾价格门槛\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>车保持安全距离，让你开车更安心。\\n\\n再说说全场景智能泊车功能，对于停车困难户来说，这就是救星！咱们都知道，现在找停车位难，停进去更难。自动泊车功能就能大显身手啦！当你开车进入停车场，它会通过超声波雷达和环视摄像头，快速探测周围的车位情况。找到合适的车位后，它自动计算出最佳的泊车轨迹，然后 “指挥” 车子稳稳地停进去。不管是侧方位停车还是倒车入库，都不在话下，新手小白也能秒变停车高手。遥控泊车功能也很有意思，要是遇到那种特别窄的车位，人很难进出。这时候，你可以通过手机蓝牙连接车辆，站在车外就能控制车子自动泊入或泊出车位。别人还在为怎么从窄车位里出来发愁的时候，你轻松用手机操作，车子就自己停好了，是不是特别酷？代客泊车功能就更方便了。比如你送孩子去学校，时间特别紧张，到了学校停车场，你在中控屏选好车位后，直接下车就行，车子会自己泊入车\\n\\n位并熄火。等你办完事回来，它还能按照你的指令再开出来，节省了好多时间。</paragraph_content>}",\n  "{<file_title>动力.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是产品功能说明，详细介绍第二代秦 PLUS DM-i 智驾版搭载的第五代 DM 技术。通过高效发动机、电混系统和刀片电池的组合，实现超低油耗（百公里亏电油耗2.93L）和超长续航（满油满电2055km），解决新能源车续航焦虑和高油耗问题。\\",\\n  \\"KeyInfo\\": [\\n    \\"第五代DM技术热效率达46.06%，工况效率92%\\",\\n    \\"百公里亏电油耗仅2.93L，油费成本大幅降低\\",\\n    \\"满油满电综合续航2055km，消除续航焦虑\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>款车，加满油、充满电，一路上根本不用为续航发愁，想去哪就去哪。就算遇到一些充电\\n\\n桩不太普及的偏远地区，也完全没压力，真正做到了畅行无阻。就算你平时上下班通勤距\\n\\n离比较远, 或者经常需要开车跑长途, 它也能轻松应对, 完全能满 足你的各种出行需求,\\n\\n续航焦虑和高油耗的烦恼通通说拜拜! 宝子们, 你们觉得这个 续航和油耗表现怎么样?</paragraph_content>}",\n  "{<file_title>智驾.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是产品功能说明，详细介绍第二代秦 PLUS DM-i 智驾版的高阶智能驾驶系统。文档重点介绍了29个传感器配置、高快领航与全场景泊车功能，并强调其以6.98万起的亲民价格打破高端智驾技术门槛。\\",\\n  \\"KeyInfo\\": [\\n    \\"搭载29个传感器的天神之眼C三目版系统\\",\\n    \\"高快领航与全场景智能泊车功能覆盖复杂场景\\",\\n    \\"6.98万起售价打破高阶智驾价格门槛\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>位并熄火。等你办完事回来，它还能按照你的指令再开出来，节省了好多时间。\\n\\n全维安全辅助功能也为咱们的行车安全上了多重保险。有时候开车不注意，前方车辆突然急刹车，要是反应不及时就容易追尾。但有了这个功能，当车子探测到前方有潜在的碰撞风险时，会先发出警报提醒你。要是你没来得及刹车，它会自动施加制动，避免碰撞或者减轻碰撞的程度，大大降低了事故的风险。\\n\\n宝子们，第二代秦 PLUS DM-i 智驾版的这些智驾功能是不是超厉害？在过去，像这么厉害的高阶智驾功能，往往只有那些价格十几万甚至二十几万的车型才配备，普通消费者想要体验这种先进的智能驾驶技术，得花上不少钱。但是现在，第二代秦 PLUS DM-i 智驾版直接打破了这个价格门槛！它以亲民到超乎想象的价格，让大家轻松拥有高阶智驾体验限时综合补贴价 6.98 万起，你就能把这辆搭载了强大智驾系统的车开回家。这性价比简直无敌了，实实在在为咱们消费者着想，花小钱就能享受到高端配置带来的便捷与舒适。\\n\\n那么大家觉得哪个功能对自己最有用呢？来评论区说说吧！</paragraph_content>}",\n  "{<file_title>政策宣讲.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是比亚迪秦PLUS智驾版车型的限时促销政策宣讲文档，重点介绍EV/DM-i双版本的三重补贴政策、旧车置换优惠及车型性能优势。核心内容包括6.98万起售的补贴价、最高1万元综合补贴以及全系开放置换资格，活动截止至4月30日。\\",\\n  \\"KeyInfo\\": [\\n    \\"三重补贴最高1万元（厂家6000元+地方4000元）\\",\\n    \\"旧车置换全系可享（含DM-i入门款）\\",\\n    \\"DM-i百公里油耗2.93L/EV续航510km\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>新车！\\n\\n政策算账：\\n\\n第二代秦 PLUS DM-i 智驾版限时综合补贴价 6.98 万起，第二代秦 PLUS EV 智驾版限时\\n\\n综合补贴价 9.98 万起，现在点击下方链接，留下您的旧车信息，专属顾问马上为您测算置换价，还在自己算账的朋友公屏扣 1，我门来帮您详细对比（配合计算器/小黑板算价）手头有旧车的家人，抓紧锁定名额！这波置换钜惠限时 4 月 30 日 24 点前可享，大家一定要抓住机会，千万不要错过了，而且到店还有更大的惊喜，主播只能跟大家透露到这里啦更多的可以右下角小风车预约，或者直接来店里，千万不要错过呀。</paragraph_content>}",\n  "{<file_title>动力.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是产品功能说明，详细介绍第二代秦 PLUS DM-i 智驾版搭载的第五代 DM 技术。通过高效发动机、电混系统和刀片电池的组合，实现超低油耗（百公里亏电油耗2.93L）和超长续航（满油满电2055km），解决新能源车续航焦虑和高油耗问题。\\",\\n  \\"KeyInfo\\": [\\n    \\"第五代DM技术热效率达46.06%，工况效率92%\\",\\n    \\"百公里亏电油耗仅2.93L，油费成本大幅降低\\",\\n    \\"满油满电综合续航2055km，消除续航焦虑\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>这套技术带来的最直观体验就是超级节省。百公里 NEDC 亏电油耗低至 2.93L 是什么概\\n\\n念？现在油价这么高，开传统燃油车，每个月光油钱就是一笔不小的开支。但开咱们这款\\n\\n车，就算在亏电状态下，油耗也低得惊人。打个比方，你和朋友都开车去隔壁城市出差，\\n\\n他开的是同级别燃油车，你开的是第二代秦 PLUS DM-i 智驾版，同样跑一百公里，他可\\n\\n能要花七八十块钱的油钱，你呢，可能二十来块钱就够了，一趟下来能省不少钱。日积月\\n\\n累，每年在油费上能省下一大笔，都够给自己买个心仪已久的高价礼物了。\\n\\n而且它还能实现超长续航，满油满电综合续航可达 2055km！这续航里程直接打破了大家\\n\\n对新能源车续航的担忧。想象一下，你计划一次长途自驾游，从沿海城市一路开到内陆，\\n\\n要是开普通新能源车，可能每隔一段路就得找充电桩，提心吊胆地担心电量不够。但开这\\n\\n款车，加满油、充满电，一路上根本不用为续航发愁，想去哪就去哪。就算遇到一些充电\\n\\n桩不太普及的偏远地区，也完全没压力，真正做到了畅行无阻。就算你平时上下班通勤距</paragraph_content>}",\n  "{<file_title>政策宣讲.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是比亚迪秦PLUS智驾版车型的限时促销政策宣讲文档，重点介绍EV/DM-i双版本的三重补贴政策、旧车置换优惠及车型性能优势。核心内容包括6.98万起售的补贴价、最高1万元综合补贴以及全系开放置换资格，活动截止至4月30日。\\",\\n  \\"KeyInfo\\": [\\n    \\"三重补贴最高1万元（厂家6000元+地方4000元）\\",\\n    \\"旧车置换全系可享（含DM-i入门款）\\",\\n    \\"DM-i百公里油耗2.93L/EV续航510km\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>引导留资：\\n\\n老板们，比亚迪限时置换政策上新，哪怕你开的是 10 年前的老车，也能折价换新！有没有开旧车想换新的？评论区扣“1”，我教你如何抓住这波限时福利！30 秒我教您怎么操作：点击右下角小风车留下你的 11 位阿拉伯数字，会有专属客服联系您，把您的车龄+车型发给客服，在线秒算补贴额度！（手机屏对准镜头）看到倒计时没有？4 月 30 号补贴通\\n\\n道准时关闭！现在下单还能抢限时福利！\\n\\n场景化种草：\\n\\n咱们每天通勤，DM-i 版 NEDC 百公里油耗低至 2.93L，省下的油钱够你喝一个月奶茶；周末自驾，EV 版 CLTC 纯电续航里程可达 510km，说走就走，再也不用找充电桩；无论是 DM-i 版还是 EV 版都能满足咱们通勤、出游的需求！旧车置换，直接抵车价，0 压力开\\n\\n新车！\\n\\n政策算账：\\n\\n第二代秦 PLUS DM-i 智驾版限时综合补贴价 6.98 万起，第二代秦 PLUS EV 智驾版限时</paragraph_content>}",\n  "{<file_title>aian_car_data.md</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是新能源汽车产品说明文档，详细介绍了埃安AION RT 2025款650智豪版的车型参数。文档涵盖价格区间11.98-16.58万元，CLTC纯电续航650公里，配备L2级辅助驾驶系统等核心信息。\\",\\n  \\"KeyInfo\\": [\\n    \\"厂商指导价13.58万元，续航里程650公里\\",\\n    \\"支持L2级辅助驾驶及360度全景影像\\",\\n    \\"配备ADiGO智能系统和8.88英寸全液晶仪表盘\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>副驾驶位后排可调节按钮: -\\n后排座椅放倒形式: 是比例放倒\\n前/后中央扶手: 前是/后-\\n后排杯架: -\\n扬声器数量: 是6喇叭\\n空调温度控制方式: 是自动空调\\n后排独立空调: -\\n后座出风口: 是\\n温度分区控制: 是\\n车载空气净化器: -\\n车内PM2.5过滤装置: 是\\n外观颜色: 水晶紫:#DDBFD6;日冕红:#C23330;月华米:#F3EBE0;海荧灰:#667896;全息银:#DCDCDC;夜影黑:#000000;极地白:#FFFFFF\\n内饰颜色: 黑色/米色:#000000/#D3C8BE;黑色:#000000;黑色/橙色:#000000/#B45014\\n接近角(°): 14\\n离去角(°): 17\\n道路救援呼叫: 是\\nCLTC纯电续航里程(km): 650\\n电池快充电量范围(%): 30-80\\n电能当量燃料消耗量(L/100km): 1.38\\n电动机总马力(Ps): 224\\n百公里耗电量(kWh/100km): 11.9\\n前方碰撞预警: 是\\n低速行车警告: 是\\n能量回收系统: 是\\n摄像头数量: 是5个\\n超声波雷达数量: 是3个\\n毫米波雷达数量: 是1个\\n辅助驾驶等级: 是L2\\n车道居中保持: 是\\n道路交通标识识别: 是\\n远程启动功能: 是\\n电池预加热: 是\\n车载智能系统: 是ADiGO\\n4G/5G网络: 是4G\\n手机APP远程功能: 是车门控制;是车窗控制;是车辆启动;是充电管理;是车灯控制;是空调控制;是车况查询/诊断;是车辆定位/寻车;是车主服务（查找充电桩、加油站、停车场等）;是预约保养/维修\\n换挡形式: 是电子式怀挡换挡\\n电池冷却方式: 液冷\\n快充功能: 支持\\n内置行车记录仪: 是\\n透明底盘/540度影像: 是\\n地图品牌: 是高德\\n隐藏电动门把手: 是\\n对外放电: 是\\nWi-Fi热点: 是\\nETC装置: ○(200元)\\n电芯品牌: 宁德时代\\n语音分区域唤醒识别: 是双区域\\n热泵空调: 是\\n前方感知摄像头: 是单目\\n应用商店: 是\\n三电首任车主质保政策: 终身质保/非营运（责任免除条款以官方为准）\\n慢充接口位置: 车右前侧\\n前电动机品牌: 锐湃动力\\n前电动机型号: TZ180XYXOF51\\n快充接口位置: 车右前侧\\n对外交流放电功率(kW): 3.3\\n辅助驾驶系统: 是ADiGO\\n语音助手唤醒词: 是你好，北鼻\\n语音连续识别: 是</paragraph_content>}",\n  "{<file_title>动力.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是产品功能说明，详细介绍第二代秦 PLUS DM-i 智驾版搭载的第五代 DM 技术。通过高效发动机、电混系统和刀片电池的组合，实现超低油耗（百公里亏电油耗2.93L）和超长续航（满油满电2055km），解决新能源车续航焦虑和高油耗问题。\\",\\n  \\"KeyInfo\\": [\\n    \\"第五代DM技术热效率达46.06%，工况效率92%\\",\\n    \\"百公里亏电油耗仅2.93L，油费成本大幅降低\\",\\n    \\"满油满电综合续航2055km，消除续航焦虑\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>动力：\\n\\n接下来咱们说说第二代秦 PLUS DM-i 智驾版的 “动力心脏”—— 第五代 DM 技术，这可\\n\\n是实打实的经过市场验证的黑科技！\\n\\n它是以插混专用 1.5L 高效发动机、EHS 电混系统和插混专用功率型刀片电池为核心构建的，打造出一套以电为主的混动技术，把节能和性能完美融合到了一起。这插混专用 1.5L高效发动机，热效率高达 46.06%，在燃油转化为动力这方面，那效率杠杠的。配合上EHS 电混系统，工况效率也提升到了 92%，两者默契配合，还能智能决定电机驱动、发动机驱动、发动机发电的时机，可油可电，灵活得很。\\n\\n再说说这插混专用功率型刀片电池，不仅容量大，而且兼顾了功率密度和能量密度，采用低阻抗设计，还拓展了电池策略应用窗口，安全方面也完全不用担心，满足比亚迪严苛的针刺试验标准。有了它，车辆的动力输出更稳定，续航也更有保障。\\n\\n这套技术带来的最直观体验就是超级节省。百公里 NEDC 亏电油耗低至 2.93L 是什么概\\n\\n念？现在油价这么高，开传统燃油车，每个月光油钱就是一笔不小的开支。但开咱们这款</paragraph_content>}",\n  "{<file_title>外观.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是产品外观设计说明，详细介绍了比亚迪第二代秦 PLUS DM-i 智驾版的外观设计特点。文档重点描述了龙颜美学设计语言、车身颜色选择及动态腰线等核心设计元素，突出其科技感与传统文化的融合。\\",\\n  \\"KeyInfo\\": [\\n    \\"延续比亚迪龙颜美学设计，展现宽体低趴姿态\\",\\n    \\"提供冰珀青/白釉青/时光灰/雪域白四种车身颜色\\",\\n    \\"游龙跃动腰线强化运动感与力量感设计\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>经典设计符号 “中国结” 的造型特征。当转向灯亮起的时候，灯光就像潺潺的流水一样把中国传统文化的独特魅力展现得淋漓尽致。\\n\\n虽然第二代秦 PLUS DM-i 智驾版的整体外观和 EV 版车型一脉相承，都有着龙颜美学的精髓，但它又有着自己独特的韵味。比如说，混动车型在一些细节设计上，可能会更偏向于凸显它的动力性能，在保持优雅的同时，多了几分力量感。就像一个既有颜值又有实力的选手，让人越看越喜欢。</paragraph_content>}",\n  "{<file_title>aian_car_data.md</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是新能源汽车产品说明文档，详细介绍了埃安AION RT 2025款650智豪版的车型参数。文档涵盖价格区间11.98-16.58万元，CLTC纯电续航650公里，配备L2级辅助驾驶系统等核心信息。\\",\\n  \\"KeyInfo\\": [\\n    \\"厂商指导价13.58万元，续航里程650公里\\",\\n    \\"支持L2级辅助驾驶及360度全景影像\\",\\n    \\"配备ADiGO智能系统和8.88英寸全液晶仪表盘\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>ABS防抱死: 是\\n制动力分配(EBD/CBC等): 是\\n刹车辅助(EBA/BAS/BA等): 是\\n牵引力控制(ASR/TCS/TRC等): 是\\n车身稳定控制(ESC/ESP/DSC等): 是\\n胎压监测功能: 是胎压显示\\n安全带未系提醒: 是\\nISOFIX儿童座椅接口: 是\\n车道偏离预警系统: 是\\n主动刹车/主动安全系统: 是\\n疲劳驾驶提示: -\\n驾驶模式切换: 是运动;是经济;是标准/舒适\\n自动驻车: 是\\n上坡辅助: 是\\n前/后驻车雷达: 前-/后是\\n驾驶辅助影像: 是360度全景影像\\n巡航系统: 是全速自适应巡航\\n卫星导航系统: 是\\n导航路况信息显示: 是\\n并线辅助: -\\n车道保持辅助系统: 是\\n轮圈材质: 是铝合金\\n电动后备厢: -\\n车内中控锁: 是\\n钥匙类型: 是遥控钥匙;是蓝牙钥匙\\n无钥匙启动系统: 是\\n无钥匙进入功能: 是前排\\n近光灯光源: 是LED\\n远光灯光源: 是LED\\nLED日间行车灯: 是\\n自适应远近光: 是\\n自动头灯: 是\\n大灯高度可调: 是\\n大灯延时关闭: 是\\n天窗类型: 是分段式不可开启天窗\\n前/后电动车窗: 前是/后是\\n车窗一键升降功能: 是全车\\n车窗防夹手功能: 是\\n车内化妆镜: 是主驾驶+照明灯;是副驾驶+照明灯\\n后雨刷: -\\n感应雨刷功能: 是雨量感应式\\n外后视镜功能: 是电动调节;是电动折叠;是后视镜加热;是锁车自动折叠\\n中控彩色屏幕: 是触控液晶屏\\n中控屏幕尺寸: 是14.6英寸\\n蓝牙/车载电话: 是\\n手机互联/映射: 是支持CarPlay;是支持HUAWEI HiCar;是支持Carlink\\n语音识别控制系统: 是多媒体系统;是导航;是电话;是空调;是座椅加热;是座椅通风\\n车联网: 是\\nOTA升级: 是\\n方向盘材质: 是皮质\\n方向盘位置调节: 是手动上下+前后调节\\n多功能方向盘: 是\\n方向盘换挡拨片: -\\n方向盘加热: -\\n方向盘记忆: -\\n行车电脑显示屏幕: 是彩色\\n全液晶仪表盘: 是\\n液晶仪表尺寸: 是8.88英寸\\n内后视镜功能: 是手动防眩目\\n多媒体/充电接口: 是USB\\nUSB/Type-C接口数量: 是前排3个/后排1个\\n座椅材质: 是仿皮\\n主座椅调节方式: 是前后调节;是靠背调节;是高低调节(2向)\\n副座椅调节方式: 是前后调节;是靠背调节\\n主/副驾驶座电动调节: 主是/副是\\n前排座椅功能: 是加热;是通风</paragraph_content>}",\n  "{<file_title>aian_car_data.md</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是新能源汽车产品说明文档，详细介绍了埃安AION RT 2025款650智豪版的车型参数。文档涵盖价格区间11.98-16.58万元，CLTC纯电续航650公里，配备L2级辅助驾驶系统等核心信息。\\",\\n  \\"KeyInfo\\": [\\n    \\"厂商指导价13.58万元，续航里程650公里\\",\\n    \\"支持L2级辅助驾驶及360度全景影像\\",\\n    \\"配备ADiGO智能系统和8.88英寸全液晶仪表盘\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content># 汽车数据\\n\\n## 第一行数据</paragraph_content>}",\n  "{<file_title>内饰.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是汽车内饰设计说明，详细介绍了第二代秦PLUS EV智驾版的内饰特点。文档通过环抱式座舱、中国元素融入及科技感材质等设计亮点，突出其舒适性与科技美学的结合，同时强调空间实用性。\\",\\n  \\"KeyInfo\\": [\\n    \\"环抱式阔境悬浮座舱营造沉浸式驾乘体验\\",\\n    \\"中控台与方向盘融合中国结等传统元素与科技设计\\",\\n    \\"提供暮云灰/砂金米配色及全平中央通道空间优化\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>接着来看看这个全新一代王朝方向盘，造型以中国结为设计中心向外发散，真的是把中国元素运用得恰到好处。握上去的那一刻，你就能感觉到它的与众不同，触感细腻得就像高级的绸缎，握感还特别紧实。而且它还有人性化的凹陷设计，手指能很自然地贴合上去，长时间开车也不会觉得累，所以说好的设计往往藏在细节里。\\n\\n还有这个全新 BYD Heart 电子换挡，真的是颜值与实用并存。挡把采用切面工艺，造型别致得很，拿在手里，感觉就像握着一件艺术品。更贴心的是，启动、换挡、驾驶模式等核心功能都集成在挡把周围，操作起来特别方便。比如说，你想要切换驾驶模式，以前可能要在一堆按钮里找半天，现在呢，手轻轻一动就能搞定，就像给驾驶加了个便捷的 “快\\n\\n捷键”。\\n\\n内饰配色方面，提供了暮云灰、砂金米两种选择。暮云灰这种黑灰搭配，满满的科技质感就像科幻电影里未来座驾的配色，特别酷，很适合追求科技感的朋友；砂金米则是以暖色为主色调，上浅下深的搭配，一坐进车里，就能感受到那种温暖与精致，仿佛把家的温馨搬进了车里，很适合喜欢温馨氛围的宝子。宝子们，你们更喜欢哪种内饰配色呢？快在弹幕里告诉主播。</paragraph_content>}",\n  "{<file_title>aian_car_data.md</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是新能源汽车产品说明文档，详细介绍了埃安AION RT 2025款650智豪版的车型参数。文档涵盖价格区间11.98-16.58万元，CLTC纯电续航650公里，配备L2级辅助驾驶系统等核心信息。\\",\\n  \\"KeyInfo\\": [\\n    \\"厂商指导价13.58万元，续航里程650公里\\",\\n    \\"支持L2级辅助驾驶及360度全景影像\\",\\n    \\"配备ADiGO智能系统和8.88英寸全液晶仪表盘\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>可见即可说: 是\\n快充功率(kW): 150\\n高压平台（V）: 400\\n电池特有技术: 弹匣电池</paragraph_content>}",\n  "{<file_title>外观.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是产品外观设计说明，详细介绍了比亚迪第二代秦 PLUS DM-i 智驾版的外观设计特点。文档重点描述了龙颜美学设计语言、车身颜色选择及动态腰线等核心设计元素，突出其科技感与传统文化的融合。\\",\\n  \\"KeyInfo\\": [\\n    \\"延续比亚迪龙颜美学设计，展现宽体低趴姿态\\",\\n    \\"提供冰珀青/白釉青/时光灰/雪域白四种车身颜色\\",\\n    \\"游龙跃动腰线强化运动感与力量感设计\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>外观：车头-车身-车尾\\n\\n在外观的颜值方面，第二代秦 PLUS DM-i 智驾版延续了咱们比亚迪超经典的龙颜美学设计，这设计真的太绝了，整车呈现出宽体、低趴的姿态，那种威严又优雅的气质，一下子就散发出来了。\\n\\n前脸：\\n\\n龙眉耀目大灯，轮廓设计得特别简洁，没有一丝多余的线条，但简洁可不代表普通。当大灯点亮的那一刻，双目有神，满满的科技感扑面而来。我上次晚上在路上看到一辆，那大灯一亮，瞬间感觉周围的车都黯然失色了。而且这可不是徒有其表，灯腔内部采用隐藏式单透镜布局，还对材质进行了优化，不仅看着更酷炫，照明效果也超棒，晚上开车视野特别清晰，安全感拉满。\\n\\n车身侧面和颜色：</paragraph_content>}",\n  "{<file_title>aian_car_data.md</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是新能源汽车产品说明文档，详细介绍了埃安AION RT 2025款650智豪版的车型参数。文档涵盖价格区间11.98-16.58万元，CLTC纯电续航650公里，配备L2级辅助驾驶系统等核心信息。\\",\\n  \\"KeyInfo\\": [\\n    \\"厂商指导价13.58万元，续航里程650公里\\",\\n    \\"支持L2级辅助驾驶及360度全景影像\\",\\n    \\"配备ADiGO智能系统和8.88英寸全液晶仪表盘\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>id: 71880\\n更新时间: 202507\\n首字母: A\\n品牌: 埃安\\n品牌_id: 313\\n车系: AION RT\\n车系_id: 7860\\n厂商: 埃安\\n厂商_id: 492\\n最低指导价(万): 11.98\\n最高指导价(万): 16.58\\n车系展示图: car_show_image/A/313/492/7860.png\\n品牌_logo: Logo/313.png\\n车型名称: AION RT 2025款 650智豪版\\n车型_id: 69916\\n销售状态: 在售\\n年款: 2025\\n上市时间: 2024.11\\n厂商指导价: 135800\\n厂商指导价(元): 13.58万\\n级别: 紧凑型车\\n能源类型: 纯电动\\n电池快充时间(小时): 0.3\\n最大功率(kW): 165\\n最大扭矩(N·m): 240\\n车身结构: 三厢车\\n电动机(Ps): 224\\n长*宽*高(mm): 4865*1875*1520\\n官方0-100km/h加速(s): 7.7\\n最高车速(km/h): 163\\n整车质保: 四年或15万公里\\n整备质量(kg): 1750\\n最大满载质量(kg): 2220\\n长度(mm): 4865\\n宽度(mm): 1875\\n高度(mm): 1520\\n轴距(mm): 2775\\n前轮距(mm): 1602\\n后轮距(mm): 1606\\n车门开启方式: 平开门\\n车门数(个): 4\\n座位数(个): 5\\n后备厢容积(L): 540\\n风阻系数(Cd): -\\n电机类型: 永磁/同步\\n电动机总功率(kW): 165\\n电动机总扭矩(N·m): 240\\n前电动机最大功率(kW): 165\\n前电动机最大扭矩(N·m): 240\\n驱动电机数: 单电机\\n电机布局: 前置\\n简称: 电动车单速变速箱\\n挡位个数: 1\\n变速箱类型: 固定齿比变速箱\\n驱动方式: 前置前驱\\n前悬架类型: 麦弗逊式独立悬架\\n后悬架类型: 扭力梁式非独立悬架\\n助力类型: 电动助力\\n车体结构: 承载式\\n前制动器类型: 通风盘式\\n后制动器类型: 盘式\\n驻车制动类型: 电子驻车\\n前轮胎规格: 215/55 R17\\n后轮胎规格: 215/55 R17\\n备胎规格: 补胎工具\\n电池类型: 磷酸铁锂电池\\n电池能量(kWh): 68.1\\n主/副驾驶座安全气囊: 主是/副是\\n前/后排侧气囊: 前是/后-\\n前/后排头部气囊(气帘): 前是/后是</paragraph_content>}",\n  "{<file_title>外观.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是产品外观设计说明，详细介绍了比亚迪第二代秦 PLUS DM-i 智驾版的外观设计特点。文档重点描述了龙颜美学设计语言、车身颜色选择及动态腰线等核心设计元素，突出其科技感与传统文化的融合。\\",\\n  \\"KeyInfo\\": [\\n    \\"延续比亚迪龙颜美学设计，展现宽体低趴姿态\\",\\n    \\"提供冰珀青/白釉青/时光灰/雪域白四种车身颜色\\",\\n    \\"游龙跃动腰线强化运动感与力量感设计\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>车身侧面和颜色：\\n\\n再说说车身颜色，提供了冰珀青、白釉青、时光灰、雪域白这四种选择，每一种都能精准戳中不同宝子的审美点。冰珀青就像春天里山间清澈的湖水；白釉青纯净得如同清晨的第一缕阳光；时光灰像是历经岁月，沉稳大气的主角；雪域白就更经典啦，洁白无瑕的颜色永远不会过时。宝子们，你们更喜欢哪种颜色呢？弹幕里留言告诉主播呀。接着看车身侧面，这游龙跃动腰线真的是神来之笔！它饱满又富有张力，就像一条正在奔腾的巨龙，沿着车身蜿蜒而上，塑造出超级流畅的侧身线条。每次看到这个腰线，我都忍不住感叹设计师的巧思。它不仅凸显了车身的年轻灵动感，好像车随时都能 “嗖” 地一下冲出去，而且分段式的设计还增强了后肩的力量感与雕塑感，让整车看起来更加修长、运动。想象一下，车从你身边疾驰而过，这腰线一闪而过，是不是特别帅？\\n\\n尾部：\\n\\n车尾部分的灵光瑞结尾灯也超有看点！贯穿式的设计搭配上动态流水转向灯，延续了王朝\\n\\n经典设计符号 “中国结” 的造型特征。当转向灯亮起的时候，灯光就像潺潺的流水一样把中国传统文化的独特魅力展现得淋漓尽致。</paragraph_content>}",\n  "{<file_title>智驾.docx</file_title>\\n<file_abstract>{\\n  \\"Summary\\": \\"这是产品功能说明，详细介绍第二代秦 PLUS DM-i 智驾版的高阶智能驾驶系统。文档重点介绍了29个传感器配置、高快领航与全场景泊车功能，并强调其以6.98万起的亲民价格打破高端智驾技术门槛。\\",\\n  \\"KeyInfo\\": [\\n    \\"搭载29个传感器的天神之眼C三目版系统\\",\\n    \\"高快领航与全场景智能泊车功能覆盖复杂场景\\",\\n    \\"6.98万起售价打破高阶智驾价格门槛\\"\\n  ]\\n}</file_abstract>\\n<paragraph_content>智驾：\\n\\n宝子们，接下来咱们好好唠唠第二代秦 PLUS DM-i 智驾版超厉害的智驾系统，搭载了天神之眼 C - 高阶智驾三目版（DiPilot 100）。全车配备了多达 29 个传感器， 分工明确，协同合作，让车子拥有了超强的感知能力，360 度无死角地感知周围的环境，路上哪怕有一点点风吹草动，都逃不过它们的 “法眼”。像高精度毫米波雷达有 5 个，能精准探测远处车辆的距离、速度和方向；12 个超声波雷达，近距离的障碍物都能被它敏锐察觉；还有 12 个高清摄像头，把车辆周围的景象看得一清二楚。这么多传感器一起工作，车子对周边环境的了解比咱们自己还仔细！\\n\\n在高速上开车，车多的时候是不是特别累？高快领航功能帮大忙！比如说你要去隔壁城市出差，在高速上开启这个功能后，它会根据你设定的导航路径，自动帮你完成一系列操作到了匝道口，它能稳稳地辅助你自动上下匝道，就算匝道弯弯曲曲的，它也能应对自如，就像有个老司机在帮你把控方向盘一样。遇到大货车的时候，它还会智慧礼让，主动和大\\n\\n车保持安全距离，让你开车更安心。</paragraph_content>}"\n]', 'type': 'variable'}, {'key': 'system_prompt', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'type': 'params'}, {'key': 'user_prompt', 'value': '用户问题：```{user_question}```\n参考文本：```{retrieved_result}```\n你的回答：', 'type': 'params'}, {'key': 'rag_63d89.output_user_input', 'value': '<think>\n好的，用户询问知道哪些车型，我需要仔细看一下提供的参考文本。首先，参考文本里多次提到比亚迪秦PLUS智驾版，包括EV和DM-i两个版本，还有埃安AION RT 2025款650智豪版的信息。\n\n先看比亚迪秦PLUS的部分。政策宣讲文档提到限时促销价格，EV版9.98万起，DM-i版6.98万起，还有三重补贴和旧车置换优惠。动力方面，DM-i版本用的是第五代DM技术，油耗2.93L，续航2055km。内饰设计有环抱式座舱，中国结元素，配色选项。外观有龙颜美学设计，四种颜色选择。智驾系统有29个传感器，支持高快领航和全场景泊车。\n\n然后是埃安AION RT，参考文本里有多个参数：价格13.58万，续航650km，L2级辅助驾驶，ADiGO系统，8.88英寸仪表盘，还有各种配置如快充、电池类型等。\n\n需要确认是否还有其他车型，但看起来只有这两款。用户要详细回答，所以应该分车型介绍，每个车型的详细参数、价格、性能、配置等。需要注意是否存在冲突信息，但比亚迪部分来自不同文档，信息一致，埃安的数据也一致，所以可以分开介绍。没有提到其他车型，所以不需要额外内容。\n\n总结：回答应包含比亚迪秦PLUS的EV和DM-i版本，以及埃安AION RT的详细信息，确保引用参考文本中的数据，结构清晰，分点说明。\n</think>\n\n根据参考文本内容，可提供以下车型的详细信息：\n\n---\n\n### **一、比亚迪 秦PLUS 智驾版（含EV/DM-i双版本）**\n#### **1. 核心参数**\n- **价格与补贴**  \n  - DM-i版：限时综合补贴价**6.98万元起**（厂家补贴6000元+地方补贴4000元，最高综合补贴1万元）。  \n  - EV版：限时综合补贴价**9.98万元起**。  \n  - 活动截止：4月30日，旧车置换全系可享（含DM-i入门款）。  \n\n- **动力与续航**  \n  - **DM-i版**：搭载第五代DM技术，百公里亏电油耗**2.93L**，满油满电综合续航**2055km**。  \n  - **EV版**：CLTC纯电续航**510km**，满足日常通勤与短途出行需求。  \n\n- **智能驾驶**  \n  - 配备**29个传感器**（5个毫米波雷达、12个超声波雷达、12个高清摄像头），搭载“天神之眼”DiPilot 100系统。  \n  - 功能亮点：  \n    - **高快领航**：支持高速自动上下匝道、弯道自动调节、大车智慧礼让。  \n    - **全场景泊车**：侧方/倒车入库/遥控泊车，支持窄车位手机操控。  \n\n- **设计特点**  \n  - **外观**：龙颜美学设计，宽体低趴姿态，提供**冰珀青/白釉青/时光灰/雪域白**四种颜色。  \n  - **内饰**：环抱式悬浮座舱，中控台融入“中国结”元素，配色提供**暮云灰/砂金米**选项，后排全平中央通道提升空间实用性。  \n\n- **技术优势**  \n  - 发动机热效率**46.06%**，工况效率**92%**，刀片电池满足针刺试验标准，安全性突出。  \n\n---\n\n### **二、埃安 AION RT 2025款 650智豪版**\n#### **1. 核心参数**\n- **价格与定位**  \n  - 厂商指导价**13.58万元**，CLTC纯电续航**650km**，支持L2级辅助驾驶系统。  \n\n- **性能与配置**  \n  - **动力**：永磁同步电机，最大功率**165kW**（224Ps），峰值扭矩**240N·m**，0-100km/h加速**7.7秒**。  \n  - **续航与充电**：  \n    - 电池容量**68.1kWh**，快充**30%-80%仅需18分钟**（快充功率150kW）。  \n    - 电能当量燃料消耗量**1.38L/100km**，百公里耗电量**11.9kWh**。  \n\n- **智能与辅助驾驶**  \n  - **ADiGO 4.0系统**：8.88英寸全液晶仪表+14.6英寸中控屏，支持语音连续识别、可见即可说、OTA升级。  \n  - **L2级辅助驾驶**：包括全速域自适应巡航、360度全景影像、车道居中保持、前方碰撞预警、自动泊车等。  \n\n- **设计与空间**  \n  - **外观**：隐藏式门把手，分段式不可开启天窗，车身配色提供**水晶紫/日冕红/月华米/海荧灰/全息银/夜影黑/极地白**七种选项。  \n  - **内饰**：仿皮座椅，支持前排座椅加热/通风，后排杯架与空调出风口提升舒适性。  \n\n- **其他亮点**  \n  - 弹匣电池技术，支持对外放电（3.3kW）、电池预加热、Wi-Fi热点、ETC装置（选装）。  \n\n---\n\n### **总结**\n- **比亚迪秦PLUS智驾版**：以**高性价比**与**低油耗/长续航**为核心，适合追求经济性与智能化的用户。  \n- **埃安AION RT 650智豪版**：主打**长续航纯电**与**L2级智驾**，适合注重科技感与续航能力的消费者。  \n\n如需进一步对比或了解其他车型，请补充信息。', 'type': 'variable'}]], 'input_data': {'input_1fb3a.user_input': '你好，请问你知道什么车型？尽量详细点。'}}, 'type': 'end', 'category': 'node_run', 'intermediate_steps': '', 'files': [], 'user_id': None, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{}', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'chat_id': ''} error: Unexpected ASGI message 'websocket.send', after sending 'websocket.close'.
[2025-08-20 21:28:22.761657] [WARNING process-81064-69172 bisheng.chat.clients.workflow_client:166] - trace=1 workflow is over by other task
[2025-08-20 21:28:22.766654] [ERROR process-81064-69172 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-20 21:28:22.771654] [WARNING process-81064-69172 bisheng.chat.manager:179] - trace=1 close_client client_key=841181918b17447aaae0fa202921f3f2 not in active_clients
[2025-08-20 21:28:23.218187] [INFO process-81064-69172 bisheng.utils.http_middleware:21] - trace=7c2356d14fe8416ab21cd0590ec212e7 GET /api/v1/knowledge/info
[2025-08-20 21:28:24.115774] [INFO process-81064-69172 bisheng.utils.http_middleware:24] - trace=7c2356d14fe8416ab21cd0590ec212e7 GET /api/v1/knowledge/info 200 timecost=897.587
[2025-08-20 21:28:24.487114] [INFO process-81064-69172 bisheng.chat.clients.base:68] - trace=1 client_id=e35c99528e7b445891eaf7541254a650 trace_id=0c4381048d4249e7b3d4ce5aa6f2caf5 message={'action': 'init_data', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'logo': '', 'name': '汽车直播助手-工作流版', 'user_id': 12, 'flow_type': 10, 'update_time': '2025-08-07T15:12:45', 'id': '0c26e44b161448ca913617e3c857d627', 'description': '', 'status': 2, 'guide_word': None, 'create_time': '2025-07-31T15:46:27', 'nodes': [{'id': 'start_25626', 'data': {'v': '1', 'id': 'start_25626', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': [''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': '1afc3c', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 614}, 'position': {'x': 720, 'y': 20}, 'selected': False}, {'id': 'input_1fb3a', 'data': {'v': '2', 'id': 'input_1fb3a', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '输入文本内容', 'global': 'key'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}]}]}, 'type': 'flowNode', 'measured': {'width': 334, 'height': 420}, 'position': {'x': 1155, 'y': 15}, 'selected': False}, {'id': 'rag_63d89', 'data': {'v': '1', 'id': 'rag_63d89', 'name': '文档知识库问答', 'type': 'rag', 'description': '根据用户问题从知识库中检索相关内容，结合检索结果调用大模型生成最终结果，支持多个问题并行执行。', 'group_params': [{'name': '知识库检索设置', 'params': [{'key': 'user_question', 'help': '当选择多个问题时，将会多次运行本节点，每次运行时从批量问题中取一项进行处理。', 'test': 'var', 'type': 'user_question', 'label': '用户问题', 'value': ['input_1fb3a.user_input'], 'varZh': {'input_1fb3a.user_input': '输入/user_input'}, 'global': 'self=user_prompt', 'linkage': 'output_user_input', 'required': True, 'placeholder': '请选择用户问题'}, {'key': 'knowledge', 'type': 'knowledge_select_multi', 'label': '检索范围', 'value': {'type': 'knowledge', 'value': [{'key': 124, 'label': '比亚迪-秦PLUS-车型知识库'}]}, 'required': True, 'placeholder': '请选择知识库'}, {'key': 'user_auth', 'help': '开启后，只会对用户有使用权限的知识库进行检索。', 'type': 'switch', 'label': '用户知识库权限校验', 'value': False}, {'key': 'max_chunk_size', 'help': '通过此参数控制最终传给模型的知识库检索结果文本长度，超过模型支持的最大上下文长度可能会导致报错。', 'type': 'number', 'label': '检索结果长度', 'value': 15000}, {'key': 'retrieved_result', 'type': 'var', 'label': '检索结果', 'global': 'self=user_prompt'}]}, {'name': 'AI回复生成设置', 'params': [{'key': 'system_prompt', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一位知识库问答助手，遵守以下规则回答问题：\n1. 请用中文严谨、专业地回答用户的问题。\n2. 回答时须严格基于【参考文本】中的内容：\n\n- 如果【参考文本】中有明确与用户问题相关的文字内容，请依据相关内容进行回答；如果【参考文本】中没有任何与用户问题相关的内容，则直接回复：“没有找到相关内容”。\n- 如果相关内容中包含 markdown 格式的图片（例如 ![image](路径/IMAGE_1.png)），必须严格保留其原始 markdown 格式，不得添加引号、代码块（`或```）或其他特殊符号，也不得修改图片路径，保证可以正常渲染 markdown 图片。\n3. 当【参考文本】中的内容来源于多个不同的信息源时，若相关内容存在明显差异或冲突，请分别列出这些差异或冲突的答案；若无差异或冲突，只给出一个统一的回答即可。', 'required': True}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '用户问题：```{{#rag_63d89.user_question#}}```\n参考文本：```{{#rag_63d89.retrieved_result#}}```\n你的回答：', 'varZh': {'rag_63d89.user_question': 'user_question', 'rag_63d89.retrieved_result': 'retrieved_result'}, 'required': True}, {'key': 'model_id', 'type': 'bisheng_model', 'label': '模型', 'value': 82, 'required': True, 'placeholder': '请在模型管理中配置 LLM 模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output_user_input', 'help': '模型输出内容将会存储在该变量中。', 'type': 'var', 'label': '输出变量', 'value': [{'key': 'output_user_input', 'label': 'output_user_input'}], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1097}, 'position': {'x': 1575, 'y': 15}, 'selected': True}], 'edges': [{'id': 'xy-edge__input_1fb3aright_handle-rag_63d89left_handle', 'type': 'customEdge', 'source': 'input_1fb3a', 'target': 'rag_63d89', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__start_25626right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'start_25626', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__rag_63d89right_handle-input_1fb3aleft_handle', 'type': 'customEdge', 'source': 'rag_63d89', 'target': 'input_1fb3a', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': -86.75296262534175, 'y': 25.15496809480402, 'zoom': 0.6563354603463992}}}
[2025-08-20 21:28:24.559801] [INFO process-81064-69016 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:28:24.596111] [INFO process-81064-71200 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:28:25.598433] [INFO process-81064-69016 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-20 21:28:25.614190] [INFO process-81064-71200 bisheng.utils.threadpool:67] - trace=0c4381048d4249e7b3d4ce5aa6f2caf5 async_task_added fun=wrapper_task args=0c4381048d4249e7b3d4ce5aa6f2caf5
[2025-08-20 21:28:41.373039] [INFO process-81064-69172 bisheng.chat.clients.base:68] - trace=1 client_id=e35c99528e7b445891eaf7541254a650 trace_id=044b0942e55249ab8cac89a8cf4c55cd message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': '你好', 'dialog_files_content': []}, 'message': '你好', 'message_id': '6f75ae0eab114d1990d0f6c4ab0cfd4b', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 21:28:41.400420] [INFO process-81064-62364 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:28:41.420557] [INFO process-81064-29680 bisheng.chat.clients.workflow_client:182] - trace=044b0942e55249ab8cac89a8cf4c55cd get user input: {'input_1fb3a': {'data': {'user_input': '你好', 'dialog_files_content': []}, 'message': '你好', 'message_id': '6f75ae0eab114d1990d0f6c4ab0cfd4b', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 21:28:42.419499] [INFO process-81064-62364 bisheng.utils.threadpool:67] - trace=044b0942e55249ab8cac89a8cf4c55cd async_task_added fun=wrapper_task args=044b0942e55249ab8cac89a8cf4c55cd
[2025-08-20 21:30:10.030655] [INFO process-81064-69172 bisheng.chat.clients.base:68] - trace=1 client_id=e35c99528e7b445891eaf7541254a650 trace_id=71565a81e9344af2bc3359bf308f84a4 message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': '你好吗？请问比亚迪是什么车？', 'dialog_files_content': []}, 'message': '你好吗？请问比亚迪是什么车？', 'message_id': '09aca8ea70344559a0922fdf3eb9a94b', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 21:30:10.054041] [INFO process-81064-69016 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:30:10.074578] [INFO process-81064-6172 bisheng.chat.clients.workflow_client:182] - trace=71565a81e9344af2bc3359bf308f84a4 get user input: {'input_1fb3a': {'data': {'user_input': '你好吗？请问比亚迪是什么车？', 'dialog_files_content': []}, 'message': '你好吗？请问比亚迪是什么车？', 'message_id': '09aca8ea70344559a0922fdf3eb9a94b', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 21:30:11.074844] [INFO process-81064-69016 bisheng.utils.threadpool:67] - trace=71565a81e9344af2bc3359bf308f84a4 async_task_added fun=wrapper_task args=71565a81e9344af2bc3359bf308f84a4
[2025-08-20 21:32:24.272571] [INFO process-81064-69172 bisheng.chat.clients.base:68] - trace=1 client_id=e35c99528e7b445891eaf7541254a650 trace_id=5a1f7fb19ac643c991fd837c8f04edce message={'action': 'input', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'data': {'input_1fb3a': {'data': {'user_input': '你好', 'dialog_files_content': []}, 'message': '你好', 'message_id': '1b12143562ad493baecacd17ccdd8109', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-20 21:32:24.307114] [INFO process-81064-31420 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-20 21:32:24.327327] [INFO process-81064-83256 bisheng.chat.clients.workflow_client:182] - trace=5a1f7fb19ac643c991fd837c8f04edce get user input: {'input_1fb3a': {'data': {'user_input': '你好', 'dialog_files_content': []}, 'message': '你好', 'message_id': '1b12143562ad493baecacd17ccdd8109', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-20 21:32:25.325226] [INFO process-81064-31420 bisheng.utils.threadpool:67] - trace=5a1f7fb19ac643c991fd837c8f04edce async_task_added fun=wrapper_task args=5a1f7fb19ac643c991fd837c8f04edce
[2025-08-20 21:34:24.697962] [ERROR process-81064-69172 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001947E79B010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 60310, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001947E79A050>
              └ <__main__.PyDB object at 0x000001947E775570>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001947E79A0E0>
           └ <__main__.PyDB object at 0x000001947E775570>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001947E17EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001943E81AC80>
    │       └ <function run at 0x000001943DACCCA0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001943DACC700>
    └ <uvicorn.server.Server object at 0x00000194457E47C0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001943DACC790>
           │       │   └ <uvicorn.server.Server object at 0x00000194457E47C0>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001947E7DD6C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001947E7DE170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001947E7DE200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001947E69FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=6>
           │    └ <_ProactorSocketTransport fd=8352 read=<_OverlappedFuture finished result=10> write=<_OverlappedFuture finished result=6> wri...
           └ <_OverlappedFuture finished result=13>

AssertionError: assert f is self._write_fut
[2025-08-20 21:59:07.705077] [INFO process-81064-69172 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1001, None)
[2025-08-20 21:59:07.714450] [INFO process-81064-69172 bisheng.chat.manager:181] - trace=1 close_client client_key=e35c99528e7b445891eaf7541254a650
[2025-08-20 21:59:08.402111] [ERROR process-81064-81880 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 12, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "e35c99528e7b445891eaf7541254a650"}', 'flow_id': '0c26e44b161448ca913617e3c857d627', 'chat_id': ''} error: Unexpected ASGI message 'websocket.send', after sending 'websocket.close'.
[2025-08-20 21:59:08.485857] [ERROR process-81064-69172 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-20 21:59:08.492273] [WARNING process-81064-69172 bisheng.chat.manager:179] - trace=1 close_client client_key=e35c99528e7b445891eaf7541254a650 not in active_clients
[2025-08-20 21:59:27.882953] [INFO process-81064-69172 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_841181918b17447aaae0fa202921f3f2 task=<Future at 0x19445acd330 state=finished returned Future> res=False
[2025-08-20 21:59:27.889124] [INFO process-81064-69172 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=953b56589a57417ebdf2a3b8c94cfb0b task=<Future at 0x19445b8cd30 state=finished returned Future> res=False
[2025-08-20 21:59:27.895125] [INFO process-81064-69172 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=25b81746967d48bd8e823c439ca2912a task=<Future at 0x19445b8e110 state=finished returned Future> res=False
[2025-08-20 21:59:27.902760] [INFO process-81064-69172 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_e35c99528e7b445891eaf7541254a650 task=<Future at 0x19445e8afb0 state=finished returned Future> res=False
[2025-08-20 21:59:27.908896] [INFO process-81064-69172 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=0c4381048d4249e7b3d4ce5aa6f2caf5 task=<Future at 0x19445e8aa10 state=finished returned Future> res=False
[2025-08-20 21:59:27.917029] [INFO process-81064-69172 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=044b0942e55249ab8cac89a8cf4c55cd task=<Future at 0x19445ef2560 state=finished returned Future> res=False
[2025-08-20 21:59:27.925508] [INFO process-81064-69172 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=71565a81e9344af2bc3359bf308f84a4 task=<Future at 0x19445ef1cc0 state=finished returned Future> res=False
[2025-08-20 21:59:27.932639] [INFO process-81064-69172 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=5a1f7fb19ac643c991fd837c8f04edce task=<Future at 0x19445eced70 state=finished returned Future> res=False
