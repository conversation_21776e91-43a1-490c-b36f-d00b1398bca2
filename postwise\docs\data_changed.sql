--  knowledge 表新增标签字段，用于区别不同的知识库。比如：医疗，金融等等。
ALTER TABLE `knowledge`
    ADD COLUMN `tags` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL;

-- llm_server 表修改 description 字段长度。 用于表示服务提供方的描述，比如，通义千问这个服务提供方（提供方内部也有模型的描述），所以这里修改为 text 类型。
ALTER TABLE `llm_server` MODIFY COLUMN `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 添加新的tags字段（JSON类型存储字符串数组）
ALTER TABLE llm_model ADD COLUMN tags JSON NULL COMMENT '模型标签列表，如["高效", "量化", "长上下文"]';