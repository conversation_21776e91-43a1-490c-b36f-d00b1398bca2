-- -- 每当有数据库变更时，不管是【Docker】还是【开发库】，还是【生产库】，都需要执行对应语句并接着记录在该SQL文件中。
SHOW DATABASES;
USE bisheng;

-- --------------------------------------------------------------------------------------------------------------
--  knowledge 表新增标签字段，用于区别不同的知识库。比如：医疗，金融等等。
ALTER TABLE `knowledge`
    ADD COLUMN `tags` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL;

-- llm_server 表修改 description 字段长度。 用于表示服务提供方的描述，比如，通义千问这个服务提供方（提供方内部也有模型的描述），所以这里修改为 text 类型。
ALTER TABLE `llm_server` MODIFY COLUMN `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 添加新的tags字段（JSON类型存储字符串数组）
ALTER TABLE llm_model ADD COLUMN tags JSON NULL COMMENT '模型标签列表，如["高效", "量化", "长上下文"]';

-- Qwen3系列模型
UPDATE `bisheng`.`llm_model`
SET `tags` = '["Qwen3", "大模型"]'
WHERE `model_name` = '/models/Qwen3-235B-A22B';

-- Qwen2.5-VL系列模型（保留原有有效标签）
UPDATE `bisheng`.`llm_model`
SET `tags` = '["Qwen2.5", "多模态", "长上下文"]'
WHERE `model_name` = '/models/Qwen2.5-VL-72B-Instruct';

-- qwen3基础模型
UPDATE `bisheng`.`llm_model`
SET `tags` = '["Qwen3", "对话"]'
WHERE `model_name` = 'qwen3';

-- 嵌入模型
UPDATE `bisheng`.`llm_model`
SET `tags` = '["BGE", "嵌入模型"]'
WHERE `model_name` = 'bge-large-zh-v1.5';

-- 重排序模型
UPDATE `bisheng`.`llm_model`
SET `tags` = '["BGE", "重排序"]'
WHERE `model_name` = 'bge-reranker-large';

-- --------------------------------------------------------------------------------------------------------------

